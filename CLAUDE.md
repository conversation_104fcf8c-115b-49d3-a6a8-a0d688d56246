# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此仓库中工作时提供指导。

## 构建命令

### 单组件构建
```bash
go build -tags=<组件名> -o server
```
其中 `<组件名>` 可以是: biz, classify, embedding, embedding_local, model_server, prerank, process, qubase, rank, recall_intervene, rerank, seg

### 多组件构建
```bash
go build -tags=<组件1>,<组件2>,<组件3> -o server
```

### 所有服务构建
```bash
go build -tags=all,boot_all,cloud_unlock -o lynxiao-ai-search
# 或使用构建脚本:
./build_all.sh
```

### ARM64构建
```bash
./build_all_arm64.sh
```

## 运行服务

### 单个服务
```bash
./server server -c ./config/<服务名>
```

### 所有服务
```bash
./lynxiao-ai-search server -c ./config/all
# 或使用运行脚本:
./docker_cmd.sh
```

## 测试

测试文件位于 `test/` 目录下，每个服务都有单独的测试文件:
```bash
go test ./test/<服务名>/...
```

## 架构概览

这是一个模块化的AI搜索系统，使用Go 1.23.3构建，采用基于组件的架构:

### 核心组件
- **seg**: 文本分词和查询解析服务
- **prerank**: 文档预排序和初筛服务  
- **rank**: 主排序服务，集成ML模型
- **rerank**: 后排序服务，包含13种专业重排策略(5种医疗、4种体育、2种新闻、2种通用)
- **embedding**: 文本向量化服务(本地和ASE两种版本)
- **process**: 文档处理和过滤管道
- **biz**: 业务逻辑服务
- **classify**: 文档分类服务
- **qubase**: 查询理解和基础处理
- **recall_intervene**: 召回干预和资源管理

### 构建系统
- 使用Go构建标签进行条件编译 (//go:build 标签)
- 每个服务都有自己的init()函数向主应用注册
- 服务在 `app/` 下注册，在 `internal/` 下实现
- 配置文件按服务组织在 `config/` 下

### 服务初始化流程
1. `main.go` 调用 `cmd.Init()` 解析命令行参数
2. `goboot.InitFromConfig()` 从TOML配置初始化组件
3. `app.Init()` 注册并启动所有标记的服务
4. `goboot.RunServer()` 启动HTTP监听器

### 错误处理
- 自定义5位错误码: 前缀(业务类型) + 后缀(错误类型)
- 通用错误: 10xxx, 精排: 11xxx, 粗排: 12xxx等
- 错误定义在 `error/` 目录下，每个服务有专门文件

### Docker与部署
- `cicd/` 目录下有不同服务组合的Dockerfile
- 支持x86和ARM64架构
- 使用Skynet Pandora框架进行服务管理和可观测性

### 主要依赖库
- Gin用于HTTP路由
- MongoDB驱动用于数据库操作
- 各种ML/NLP库用于文本处理
- Pandora框架用于服务基础设施

### 开发约定
- 代码修改必须符合Go最佳实践并通过自测
- 遵循模块化架构，每个服务在 `internal` 目录下独立开发
- 使用TOML文件进行配置，具有集中的配置加载机制
- 具有完善的自定义错误处理系统
- 使用Go模块进行依赖管理