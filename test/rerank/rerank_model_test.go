package main

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	onnx_conf "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/conf"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/proto"
	onnx_runtime "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/runtime"
	tokenizer_wrapper "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/test/tokenizer_wrapper_v2"
	onnx_tools "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/tools"
)

func TestRerank(t *testing.T) {
	// 1. 读取配置并初始化推理实例
	conf := &onnx_conf.OnnxConfig{
		ModelPath:        "/data/bak/wfliu3/lynxiao/lynxiao-ai-search/scripts/reranking_model_float.onnx",
		LibPath:          "/data/bak/wfliu3/lynxiao/lynxiao-ai-search/resource/tokenizer/libonnxruntime.so",
		DeviceType:       "cpu",
		EngineType:       "onnxruntime",
		DeviceID:         0,
		InferThreadNum:   1,
		InferQueueLength: 1024,
	}

	rt, err := onnx_runtime.NewRuntime(conf)
	if err != nil {
		t.Fatal(err)
	}

	queries := []string{"26岁女孩如何提高基础代谢", "26岁女孩如何提高基础代谢"}
	answers := []string{"26岁如何提高新陈代谢率", "女生想提高基础代谢率怎么吃？"}

	// 2. 输入转token,该步骤应放到节点服务中，与推理服务分离，这里仅演示用
	// 加载动态库
	libHandle, err := tokenizer_wrapper.LoadEngineLib("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/resource/tokenizer/libtokenizer_wrapper.so")
	if err != nil {
		t.Fatalf("load lib failed: %v", err)
	}
	defer tokenizer_wrapper.CloseEngineLib(libHandle)

	modelName := "/data/bak/wfliu3/lynxiao/lynxiao-ai-search/resource/tokenizer/Xenova-ernie-3.0-nano-zh/tokenizer.json"
	useLocal, padID, padFixed, maxLength := 1, 0, 0, 512
	tokenizer := tokenizer_wrapper.NewTokenizerWrapper(modelName, useLocal, padID, padFixed, maxLength)
	encodings, _ := tokenizer.EncodeBatchPair(queries, answers, true)

	inputDatas := &proto.InferReq{
		Inputs: make(map[string][]byte),
		Shapes: make(map[string][]int64),
	}

	var seqLen int
	batchSize := int64(len(encodings))
	for _, encoding := range encodings {
		seqLen = len(encoding.Ids)
		fmt.Printf("%v\n", encoding.Ids)
		inputDatas.Inputs["input_ids"] = append(inputDatas.Inputs["input_ids"], onnx_tools.SliceToBytes(encoding.Ids)...)
		inputDatas.Inputs["token_type_ids"] = append(inputDatas.Inputs["token_type_ids"], onnx_tools.SliceToBytes(encoding.Types)...)
		inputDatas.Inputs["attention_mask"] = append(inputDatas.Inputs["attention_mask"], onnx_tools.SliceToBytes(encoding.Masks)...)
	}

	inputDatas.Shapes["input_ids"] = []int64{batchSize, int64(seqLen)}
	inputDatas.Shapes["token_type_ids"] = []int64{batchSize, int64(seqLen)}
	inputDatas.Shapes["attention_mask"] = []int64{batchSize, int64(seqLen)}

	// jsonByte, _ := json.Marshal(inputDatas)
	// fmt.Println(string(jsonByte))

	resMap, err := rt.Infer(context.TODO(), inputDatas)
	fmt.Println(resMap)

	jsonByte, _ := json.Marshal(resMap)
	fmt.Println(string(jsonByte))
	if err != nil {
		t.Fatal(err)
	}

	// 3. 模型输出处理(对于分类任务，通常采用sigmoid或者argmax进行处理)
	for name, res := range resMap.Outputs {
		logits := onnx_tools.BytesToSlice[float32](res)
		fmt.Println(name, logits)
	}
}
