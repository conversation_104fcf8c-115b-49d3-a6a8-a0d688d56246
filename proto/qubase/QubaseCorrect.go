package proto_qubase

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

type ProtoCorrectResponsePayload struct {
	Results []string `json:"results"`
}

type ProtoCorrectRequestPayload struct {
	Texts           []string `json:"query"`
	CorrectStrategy []string `json:"correctStrategy"`
}

var ProtoCorrectAPIV2 = pandora.NewPandoraProto[ProtoCorrectRequestPayload, ProtoCorrectResponsePayload]()
