package biz

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

type ProtoBizAPIV2Request struct {
	Query      []string `json:"query"`
	Business   string   `json:"business" default:"lynxiao-flow"`
	Limit      int      `json:"limit" default:"5"`
	FullText   bool     `json:"full_text" default:"true"`   // 改 Full_text 为 FullText
	OpenRerank bool     `json:"open_rerank" default:"true"` // 改 Open_rerank 为 OpenRerank
	Highlight  bool     `json:"highlight" default:"false"`
	Crawler    bool     `json:"crawler" default:"false"`
	Pipeline   string   `json:"pipeline" default:"pl_map_agg_search_medical"`
}

type ProtoBizAPIV2Response struct {
	Results []*ProtoBizAPIV2BizSearchData `json:"results"`
}

type ProtoBizAPIV2BizSearchData struct {
	Query string                     `json:"query"`
	Code  int                        `json:"code"`
	Msg   string                     `json:"message"`
	Data  []*ProtoBizAPIV2SearchItem `json:"docs"`
}

type ProtoBizAPIV2SearchItem struct {
	Title           string  `json:"title"`
	Url             string  `json:"url"`
	Score           float64 `json:"score"`
	Summary         string  `json:"summary"`
	Img             string  `json:"img"`
	FrScore         float64 `json:"fr_score"`
	TimeWeightScore float64 `json:"time_weight_score"`
	Source          string  `json:"source"`
	TimeScore       float64 `json:"time_score"`
	Content         string  `json:"content"`
	SiteName        string  `json:"site_name"`
	SiteWeightScore float64 `json:"site_weight_score"`
	URLRepeatScore  float64 `json:"url_repeat_score"`
	DailyDiff       int64   `json:"daily_diff"`
	PublishedDate   string  `json:"published_date"`
	ExtractDate     string  `json:"extract_date"`
}

var ProtoBizAPIV2 = pandora.NewPandoraProto[ProtoBizAPIV2Request, ProtoBizAPIV2Response]()
