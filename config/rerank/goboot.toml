[goboot]
enabled=true
service_name="rerank_test"

#server 配置
[[http_server]]
enabled=true
port=50903

[http_server.metrics]
url="/actuator/prometheus"

#tlb配置
[tlb_sdk]
enabled=true
#servers="**************:30132"
servers = "**************:30132"
port=50903
tlb_tag="A"

#默认日志
[[loggers]]
enabled=true
file_path="/datas/logs/goboot_test.json"
console=true
log_level="debug"

[elk]
enabled=true
name="elk_log"
log_level="debug"
tags="region:hf,env:test"

[elk.local_log]
reuse="default"
log_level="debug"

[elk.kafka]
brokers = "kafka-0.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-1.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-2.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092"
topic = "lynxiao_flow"

[cloud_lock]
enabled=false
# lib 库路径
lib_path = "/share/auth_demo/python-SDK/libdongle.so"
# 授权文件路径
auth_file_path = "/share/dongle"
# 产品名称
product_name = "Lynxiao"
# 版本号，必须是三位，eg：1.0.0
version = "2.1.0"
# 授权码，hasp授权时为空字符串
author_code = ""

