[classify]
config_path = "./resource/classify/classify.json"


[[ants_pools]]
enabled = true
goroutine_num=1024



[[ases]]
enabled = true
# 模型版本名称
name = "medcs"
# ase appid
app_id = "9d4f7017"
# ase apikey
api_key = "461a9ca5ba93757f2e7d8aa9e33b818c"
# ase apiSecret
api_secret = "NWJlZDU2MmY4MTFlY2RjNGQ3NjJkYzhm"
# ase 调用url
ase_url = "http://cn-huadong-1.xf-yun.com/v1/private/sbf1b96b8"
# tlb发现的名称，用于服务发现
tlb_server = "ai-model-server-v2-2"
# ip地址调用
local_url = "**************:32173/medcs_v20250829"
#0 ase 模式，1 tlb 模式, 2 ip 地址模式
mode = 2
# 调用超时时间
timeout_mills = 2000


[[ases]]
enabled = true
# 模型版本名称
name = "domain"
# ase appid
app_id = "9d4f7017"
# ase apikey
api_key = "461a9ca5ba93757f2e7d8aa9e33b818c"
# ase apiSecret
api_secret = "NWJlZDU2MmY4MTFlY2RjNGQ3NjJkYzhm"
# ase 调用url
ase_url = "http://cn-huabei-1.xf-yun.com/v1/private/se6a4fee2"
# tlb发现的名称，用于服务发现
tlb_server = "ai-model-server-v2"
# ip地址调用
local_url = "127.0.0.1:32030/prerank_v20241212"
#0 ase 模式，1 tlb 模式, 2 ip 地址模式
mode = 0
# 调用超时时间
timeout_mills = 2000


[[tokenizers]]
enabled = true
# tokenizer名称，名称唯一，一般使用tokenizer名称
name = "Xenova-ernie-3.0-domain" 
# tokenizer资源文件，需要指定xxx/tokenizer.json
path = "./resource/tokenizer/Xenova-ernie-3.0-nano-zh/tokenizer.json"
# tokenizer依赖的.so，一般不动
lib = "./resource/tokenizer/libtokenizer_wrapper.so"
# 是否使用本地资源，由于网络原因及部署原因，一般使用本地
use_local = 1
# pad_id pad填充
pad_id = 0
# padding填充是否固定
pad_fixed = 1
# padding最大长度
max_length = 128
# tokenizer工作协程数
worker_num = 3
# tokenizer工作队列
queue_num = 2048


[[tokenizers]]
enabled = true
# tokenizer名称，名称唯一，一般使用tokenizer名称
name = "Xenova-ernie-3.0-medcs" 
# tokenizer资源文件，需要指定xxx/tokenizer.json
path = "./resource/tokenizer/medcs/tokenizer.json"
# tokenizer依赖的.so，一般不动
lib = "./resource/tokenizer/libtokenizer_wrapper.so"
# 是否使用本地资源，由于网络原因及部署原因，一般使用本地
use_local = 1
# pad_id pad填充
pad_id = 0
# padding填充是否固定
pad_fixed = 1
# padding最大长度
max_length = 128
# tokenizer工作协程数
worker_num = 3
# tokenizer工作队列
queue_num = 2048