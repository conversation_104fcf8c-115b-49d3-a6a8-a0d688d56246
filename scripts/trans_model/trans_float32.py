import onnx
from onnx import helper, Tensor<PERSON>roto, TypeProto

def convert_output_to_float32(input_path, output_path):
    # 加载ONNX模型
    model = onnx.load(input_path)
    graph = model.graph
    
    # 为每个输出添加Cast节点转换为float32
    new_nodes = []
    new_outputs = []
    
    for output in graph.output:
        # 创建Cast节点
        cast_node = helper.make_node(
            "Cast",
            inputs=[output.name],
            outputs=[f"{output.name}_float32"],
            to=TensorProto.FLOAT  # 指定目标类型为float32
        )
        new_nodes.append(cast_node)
        
        # 处理输出形状，支持符号化维度
        tensor_type = output.type.tensor_type
        shape = []
        for dim in tensor_type.shape.dim:
            # 检查是符号化维度还是具体数值维度
            if dim.dim_param:
                shape.append(dim.dim_param)  # 符号化参数（字符串）
            else:
                shape.append(dim.dim_value)  # 具体数值（整数）
        
        # 创建新的输出张量信息，使用正确的形状处理方式
        new_output = helper.make_tensor_value_info(
            f"{output.name}_float32",
            TensorProto.FLOAT,
            shape
        )
        new_outputs.append(new_output)
    
    # 将新节点添加到图中
    graph.node.extend(new_nodes)
    
    # 替换原输出为新输出
    del graph.output[:]
    graph.output.extend(new_outputs)
    
    # 检查模型是否有效
    onnx.checker.check_model(model)
    
    # 保存转换后的模型
    onnx.save(model, output_path)
    print(f"转换完成，模型已保存至: {output_path}")

# 使用示例
if __name__ == "__main__":
    input_model_path = "/data/bak/wfliu3/lynxiao/lynxiao-ai-search/internal/classify/scripts/checkpoint/epoch_27_Accuracy_0.9950/Classfication_model.onnx"   # 输入的float16输出模型
    output_model_path = "/data/bak/wfliu3/lynxiao/lynxiao-ai-search/internal/classify/scripts/checkpoint/epoch_27_Accuracy_0.9950/Classfication_model_fp32.onnx" # 输出的float32输出模型
    convert_output_to_float32(input_model_path, output_model_path)


