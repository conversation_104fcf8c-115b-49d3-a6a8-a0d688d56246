import os
import json
import requests


url = "http://*************:50903/rerank/api/v2"

lines = open("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/scripts/rerank0822/data/rerank_0822_request.json","r", encoding="utf-8").readlines()
with open("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/scripts/rerank0822/data/rerank_0822_response.json","w", encoding="utf-8") as fp:
    for line in lines:
        data = json.loads(line.strip())
        response = requests.post(url, json=data)

        res = response.json()
        fp.write(json.dumps(res, ensure_ascii=False) + '\n')

