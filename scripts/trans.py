import os
import json
import uuid

data = {
  "header": {
    "traceId": "07cf1b3e--ada-6-49-47-a-b9c-df1e0e47149f"
  },
  "payload": {
    "topk": -1,
    "scoreThreshold": 0.85,
    "step": 0.05,
    "model": "5001_V20250822",
    "rankCollections": [
      "医疗指南库_20250707(med_guide_20250707)",
     "医疗教科书库_20250707(med_textbook_20250707)",
      "医疗期刊库_20250707(med_journal_20250707)"
    ],
    "rankSites": [
      
    ],
    "full": False,
    "intent": "hot",
    "data": []
  }
}

lines = open("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/scripts/rerank0822/data/v20250715_data.json","r", encoding="utf-8").readlines()


fp_w = open("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/scripts/rerank0822/data/rerank_0822_request.json","w", encoding="utf-8")
for line in lines:
    data["payload"]["data"] = [json.loads(line)]
    data["header"]["traceId"] = str(uuid.uuid4())
    fp_w.write(json.dumps(data, ensure_ascii=False) + '\n')





