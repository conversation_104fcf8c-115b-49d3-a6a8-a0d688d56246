import os
import time
import json
import numpy as np
import torch
import onnxruntime as ort
from transformers import BertTokenizer
from torch.utils.data import Dataset, DataLoader


class RankingDataset(Dataset):
    def __init__(self, file_path, tokenizer, max_length_limit):
        self.data = []
        self.tokenizer = tokenizer
        self.max_length_limit = max_length_limit
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data = json.loads(line.strip())
                self.data.append(data)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        query = item['query']
        titles = item['title']

        query = query[:30]
        titles = [title[:30] for title in titles]

        if titles:
            max_title_len = max(len(title) for title in titles)
            max_length = min(max_title_len + len(query) + 3, self.max_length_limit)
        else:
            max_length = self.max_length_limit

        encoded_inputs = self.tokenizer(
            [query] * len(titles),
            titles,
            padding='max_length',
            truncation=True,
            max_length=max_length,
            return_tensors='pt'
        )

        return {
            'input_ids': encoded_inputs['input_ids'],
            'attention_mask': encoded_inputs['attention_mask'],
            'token_type_ids': encoded_inputs.get('token_type_ids', None),
            'num_titles': len(titles),
            'original_data': item
        }


def custom_collate(batch):
    item = batch[0]
    
    return {
        'input_ids': item['input_ids'],
        'attention_mask': item['attention_mask'],
        'token_type_ids': item['token_type_ids'],
        'num_titles': item['num_titles'],
        'original_data': item['original_data']
    }


# ONNX模型推理函数
def inference_onnx(session, dataloader, device):
    
    all_results = []

    # 初始化时间统计变量
    total_inference_time = 0.0
    batch_count = 0

    # 初始化NDCG值列表
    ndcg1_values = []
    ndcg3_values = []
    ndcg5_values = []
    ndcg10_values = []
    ndcg_all_values = []
    
    # 记录开始时间
    start_time = time.time()
    
    for batch in dataloader:
        # 转换为numpy数组(ONNX Runtime需要numpy输入)
        input_ids = batch['input_ids'].cpu().numpy()
        attention_mask = batch['attention_mask'].cpu().numpy()
        token_type_ids = batch['token_type_ids'].cpu().numpy() if batch['token_type_ids'] is not None else None
        num_titles = batch['num_titles']
        original_data = batch['original_data']

        # 记录推理开始时间
        batch_start_time = time.time()
        
        # 准备输入字典
        inputs = {
            'input_ids': input_ids,
            'attention_mask': attention_mask
        }
        if token_type_ids is not None:
            inputs['token_type_ids'] = token_type_ids

        print(inputs.keys())
        print(inputs['input_ids'].shape)
        print(inputs['attention_mask'].shape)
        print(inputs['token_type_ids'].shape)
        
        # ONNX推理
        outputs = session.run(None, inputs)
        logits = outputs[0]
        print(outputs)
        
        # 记录推理结束时间并计算耗时
        batch_end_time = time.time()
        total_inference_time += (batch_end_time - batch_start_time)
        batch_count += 1

        # 处理结果  
        pred_scores = logits[:num_titles]
        # result = pred_scores.tolist()
        result = original_data.copy()
        result['pred_scores'] = pred_scores.tolist()
        
        # 处理标签(如果存在)
        if 'label' in original_data:
            true_labels = original_data['label'][:num_titles]

            ndcg_results = calculate_all_ndcg(true_labels, pred_scores)
            result.update(ndcg_results)

            # 收集NDCG指标用于计算平均值
            ndcg1_values.append(ndcg_results["NDCG@1"])
            ndcg3_values.append(ndcg_results["NDCG@3"])
            ndcg5_values.append(ndcg_results["NDCG@5"])
            ndcg10_values.append(ndcg_results["NDCG@10"])
            ndcg_all_values.append(ndcg_results["NDCG@all"])
        
        all_results.append(result)
    
    # 计算总时间和平均推理时间(毫秒)
    end_time = time.time()
    total_time = end_time - start_time
    avg_inference_time = (total_inference_time / batch_count) * 1000
    
    print(f"Inference Complete!")
    print(f"Total Inference Batch: {batch_count}, Total Inference Time: {total_time:.2f} second")
    print(f"Average Inference Time: {avg_inference_time:.4f} ms per batch")

    # 输出最终NDCG指标
    print("\n=== NDCG Metrics Summary ===")
    print(f"Average NDCG@1: {np.mean(ndcg1_values):.4f}")
    print(f"Average NDCG@3: {np.mean(ndcg3_values):.4f}")
    print(f"Average NDCG@5: {np.mean(ndcg5_values):.4f}")
    print(f"Average NDCG@10: {np.mean(ndcg10_values):.4f}")
    print(f"Average NDCG@all: {np.mean(ndcg_all_values):.4f}")
    
    return all_results


# 计算NDCG
def ndcg(label_true, label_pred, k):
    # 检查输入有效
    if len(label_true) != len(label_pred):
        raise ValueError("Error")
    
    # 如果k未指定, 默认整个数组长度
    if k is None:
        k = len(label_true)
    else:
        k = min(k, len(label_true))
    
    # 按预测分数排序
    order = np.argsort(label_pred)[::-1]
    label_true_sorted = np.take(label_true, order[:k])
    
    # 计算DCG
    gains = 2 ** label_true_sorted - 1
    discounts = np.log2(np.arange(2, len(label_true_sorted) + 2))
    dcg = np.sum(gains / discounts)
    
    # 计算IDCG(理想DCG)
    ideal_order = np.argsort(label_true)[::-1]
    ideal_label_true = np.take(label_true, ideal_order[:k])
    ideal_gains = 2 ** ideal_label_true - 1
    ideal_discounts = np.log2(np.arange(2, len(ideal_label_true) + 2))
    idcg = np.sum(ideal_gains / ideal_discounts)
    
    # 防止除以零
    if idcg == 0:
        return 0
    
    return dcg / idcg

def calculate_all_ndcg(label_true, label_pred):
    # 计算NDCG@1、NDCG@3、NDCG@5、NDCG@all
    ndcg_at_1 = ndcg(label_true, label_pred, k=1)
    ndcg_at_3 = ndcg(label_true, label_pred, k=3)
    ndcg_at_5 = ndcg(label_true, label_pred, k=5)
    ndcg_at_10 = ndcg(label_true, label_pred, k=10)
    ndcg_at_all = ndcg(label_true, label_pred, k=None)
    
    return {
        "NDCG@1": ndcg_at_1,
        "NDCG@3": ndcg_at_3,
        "NDCG@5": ndcg_at_5,
        "NDCG@10": ndcg_at_10,
        "NDCG@all": ndcg_at_all
    }


def main():
    # 数据路径参数
    input_path = '/data/bak/wfliu3/lynxiao/lynxiao-ai-search/scripts/data/demo.json'
    onnx_model_path = '/data/bak/wfliu3/lynxiao/lynxiao-ai-search/scripts/reranking_model_v20250804.onnx'
    tokenizer_path = '/data/bak/wfliu3/lynxiao/lynxiao-ai-search/resource/tokenizer/rerank_v20250804'
    output_file = '/data/bak/wfliu3/lynxiao/lynxiao-ai-search/scripts/data/testset_out_onnx.json'
    
    # 推理参数
    batch_size = 1
    max_length = 64
    num_workers = 1
    
    # 设置设备
    device = 'cuda' if ort.get_device() == 'GPU' else 'CPU'
    print(device)
    
    # 加载Tokenizer
    tokenizer = BertTokenizer.from_pretrained(tokenizer_path)
    
    # 加载数据集
    inference_dataset = RankingDataset(
        input_path, 
        tokenizer, 
        max_length
    )
    
    inference_dataloader = DataLoader(
        inference_dataset,
        batch_size=batch_size,
        num_workers=num_workers,
        pin_memory=True,
        shuffle=False,
        collate_fn=custom_collate
    )

    # 加载ONNX模型
    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if device == 'cuda' else ['CPUExecutionProvider']
    session = ort.InferenceSession(onnx_model_path, providers=providers)

    # 推理
    results = inference_onnx(
        session, 
        inference_dataloader, 
        device
    )
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        for result in results:
            f.write(json.dumps(result, ensure_ascii=False) + '\n')
    print(f"Results saved to {output_file}")


if __name__ == "__main__":
    main()
