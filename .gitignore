# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/
.vscode/
lynxiao-ai-search
lynxiao-ai-hf
lynxiao-ai-*
logs
.idea
data/
elk_logs

# include
!resource/prerank/lib/*.so
.history
.py

*.onnx
checkpoint/
test_scripts/