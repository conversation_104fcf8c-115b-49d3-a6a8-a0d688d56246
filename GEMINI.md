# GEMINI.md

## 项目概述

本项目是一个名为“lynxiao-ai-search”的人工智能搜索引擎。它使用 Go 语言编写，并采用微服务架构。该项目基于科大讯飞的 Skynet 框架构建，并使用 Gin Web 框架作为其 HTTP 服务器。它集成了各种自然语言处理（NLP）和机器学习技术，以提供搜索功能。

该项目被组织成多个服务，包括：

*   **biz**：一个业务逻辑层，处理自定义配置和路由。
*   **prerank**：一个预排序服务，对文档进行初步筛选和评分。
*   **rank**：一个主排序服务，对文档进行评分和排名。
*   **embedding**：一个用于生成文本向量嵌入的服务。
*   **seg**：一个文本分词服务。
*   **classify**：一个文本分类服务。

该项目使用 MongoDB 作为其数据库，并与 ONNX 模型服务器交互以进行机器学习推理。

## 构建和运行

该项目使用 Go 构建标签来编译不同的服务。

### 构建单个服务

要构建单个服务，请使用以下命令：

```bash
go build -tags=<service_name> -o server
```

将 `<service_name>` 替换为您要构建的服务的名称（例如，`prerank`、`rank`）。

### 构建多个服务

要将多个服务构建到单个二进制文件中，请使用以下命令：

```bash
go build -tags=<service1>,<service2> -o server
```

### 构建所有服务

要构建所有服务，请使用以下命令：

```bash
go build -tags=all,boot_all,cloud_unlock -o lynxiao-ai-search
```

### 运行项目

要运行项目，请执行以下命令：

```bash
./<executable_name> server -c <config_path>
```

将 `<executable_name>` 替换为已编译二进制文件的名称（例如，`server`、`lynxiao-ai-search`），并将 `<config_path>` 替换为所需服务（或多个服务）的配置目录的路径（例如，`./config/prerank`、`./config/all`）。

## 开发约定

*   **代码增改**： 代码修改要符合golang最佳实践,且必须自测通过。
*   **模块化架构**：该项目遵循模块化架构，其中每个服务都在 `internal` 目录下的自己的包中进行开发。
*   **构建标签**：Go 构建标签用于控制最终二进制文件中包含哪些服务。
*   **配置**：该项目使用 TOML 文件进行配置，并具有集中的配置加载机制。
*   **错误处理**：该项目具有自定义的错误处理系统，为不同的服务定义了错误代码。
*   **依赖管理**：该项目使用 Go 模块进行依赖管理。