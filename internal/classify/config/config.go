package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"

	classify_model "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/model"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	token "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

func LoadServiceConfig(path string) (*classify_model.ServiceConfig, error) {
	// 添加配置文件路径检查
	if path == "" {
		return nil, fmt.Errorf("config path is empty")
	}

	configData, err := os.ReadFile(path)
	if err != nil {
		log.Fatalf("Failed to read file: %v", err)
		return nil, err
	}

	var config classify_model.ServiceConfig
	err = json.Unmarshal(configData, &config)
	if err != nil {
		log.Fatalf("Failed to unmarshal Config JSON: %v", err)
		return nil, err
	}

	// 统一处理所有模型配置
	modelConfigs := map[string]*classify_model.ModelInfo{
		"domain":     config.Domain,
		"emergency":  config.Emergency,
		"timeliness": config.Timeliness,
		"medcs":      config.Medcs,
	}

	for name, modelConfig := range modelConfigs {
		if modelConfig == nil || !modelConfig.Enabled {
			continue
		}
		if err := initModelConfig(name, modelConfig.Model); err != nil {
			return nil, err
		}
	}

	return &config, nil
}

func initModelConfig(name string, modelConfig *classify_model.Model) error {
	if len(modelConfig.Versions) == 0 {
		return fmt.Errorf("%s version is empty", name)
	}

	modelConfig.VersionsMap = make(map[string]*classify_model.Version)
	for _, version := range modelConfig.Versions {
		if goboot.AseSdk().GetInstance(version.Model) == nil {
			return errors.New("intent model not exist")
		}
		if token.G_tokenizer.GetInstance(version.Token) == nil {
			return errors.New("intent token not exist")
		}
		modelConfig.VersionsMap[version.Name] = version
	}
	if modelConfig.Default == "" {
		return errors.New("intent default version is empty")
	}
	if _, ok := modelConfig.VersionsMap[modelConfig.Default]; !ok {
		return errors.New("intent default version not exist")
	}

	return nil
}

func validateConfig(modelConfig *classify_model.Model) error {
	modelConfig.VersionsMap = make(map[string]*classify_model.Version)
	for _, version := range modelConfig.Versions {
		if goboot.AseSdk().GetInstance(version.Model) == nil {
			return errors.New("intent model not exist")
		}
		if token.G_tokenizer.GetInstance(version.Token) == nil {
			return errors.New("intent token not exist")
		}
		modelConfig.VersionsMap[version.Name] = version
	}
	if modelConfig.Default == "" {
		return errors.New("intent default version is empty")
	}
	if _, ok := modelConfig.VersionsMap[modelConfig.Default]; !ok {
		return errors.New("intent default version not exist")
	}
	return nil
}
