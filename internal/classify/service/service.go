package service

import (
	"context"
	"fmt"
	"sort"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/utils"
	proto_classify "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/classify"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type Service struct {
	Config *model.ServiceConfig
}

// 读取配置
var GService *Service

func (s *Service) RegisterRouter() {
	router := goboot.HttpServer().DefaultInstance().Router
	domain := router.Group("/domain")
	{
		domain.POST("/api/v2", proto_classify.ProtoClassifyAPIV2.GinWrapper().SetHandler(s.DomainProcess).HandlerFunc())
		domain.POST("/api/v2/medcs", proto_classify.ProtoClassifyAPIV2.GinWrapper().SetHandler(s.MedcsProcess).HandlerFunc())
	}
	emergency := router.Group("/emergency")
	{
		emergency.POST("/api/v2", proto_classify.ProtoClassifyAPIV2.GinWrapper().SetHandler(s.EmergencyProcess).HandlerFunc())
	}
	timeliness := router.Group("/timeliness")
	{
		timeliness.POST("/api/v2", proto_classify.ProtoTimelinessAPIV2.GinWrapper().SetHandler(s.TimelinessProcess).HandlerFunc())
	}
}

// 抽取公共的处理逻辑
type processConfig struct {
	ctx       *pandora_context.PandoraContext
	traceId   string
	reqModel  string
	model     *model.Model
	enabled   bool
	validator func() *errtypes.SelfError
	processor func(mvi *model.Version) ([]*model.Result, error)
}

// commonProcess函数用于处理processConfig类型的配置，并返回一个pandora_proto.PandoraResponseMessage类型的响应
func (s *Service) commonProcess(cfg processConfig) *pandora_proto.PandoraResponseMessage[model.Response] {
	span := cfg.ctx.RootSpan().AddSpan("pandora_process")
	defer span.Finish()

	var selfError *errtypes.SelfError
	resp := pandora_proto.NewPandoraResponseMessage[model.Response]()
	resp.Header.TraceId = cfg.traceId

	defer func() {
		if selfError != nil {
			span.TraceInfo("selfError", selfError)
			resp.Header.Code = selfError.Code()
			resp.Header.Success = selfError.Error()
		}
	}()

	// 路由检查
	if !cfg.enabled {
		selfError = selferrors.CommonError_RouterUnSupported
		return resp
	}

	// 参数校验
	if selfError = cfg.validator(); selfError != nil {
		return resp
	}

	// 获取模型版本
	mvi := s.getModelVersion(cfg.reqModel, cfg.model, resp)
	span.TraceInfo("mvi", mvi)

	// 执行处理
	if results, err := s.executeWithTimeout(cfg.ctx, mvi, cfg.processor); err != nil {
		selfError = err
	} else {
		resp.Payload.Results = results
	}

	return resp
}

func (s *Service) getModelVersion(reqModel string, model *model.Model, resp *pandora_proto.PandoraResponseMessage[model.Response]) *model.Version {

	if reqModel == "" {
		reqModel = model.Default
	}

	if _, ok := model.VersionsMap[reqModel]; !ok {
		reqModel = model.Default
	}
	resp.Payload.Model = reqModel
	return model.VersionsMap[reqModel]
}

func (s *Service) executeWithTimeout(ctx *pandora_context.PandoraContext, mvi *model.Version,
	processor func(mvi *model.Version) ([]*model.Result, error)) ([]*model.Result, *errtypes.SelfError) {

	ctxTimeout, cancel := context.WithTimeout(ctx.GetContext(), time.Duration(mvi.Timeout)*time.Millisecond)
	defer cancel()

	f, err := goboot.AntsPool().DefaultInstance().Submit(func() (any, error) {
		return processor(mvi)
	})

	if err != nil {
		//goboot.AntsPool().DefaultInstance().Submit 返回的是errors.New.这个判断不会生效。
		if se, ok := err.(*errtypes.SelfError); ok {
			return nil, se
		}
		return nil, selferrors.CommonError_UnKouwnError.Detaild(err.Error())
	}

	select {
	case <-f.Wait():
		res, ferr := f.Result()
		if ferr != nil {
			return nil, ferr.(*errtypes.SelfError)
		}
		result := res.([]*model.Result)
		return result, nil
	case <-ctxTimeout.Done():
		return nil, selferrors.ClassifyError_Timeout
	}
}

func (s *Service) DomainProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request]) *pandora_proto.PandoraResponseMessage[model.Response] {

	return s.commonProcess(processConfig{
		ctx:      ctx,
		traceId:  req.Header.TraceId,
		reqModel: req.Payload.Model,
		model:    s.Config.Domain.Model,
		enabled:  s.Config.Domain.Enabled,
		validator: func() *errtypes.SelfError {
			return s.ValidateParameters(req)
		},
		processor: func(mvi *model.Version) ([]*model.Result, error) {

			scores, err := s.RunToken(ctx, req.Payload.Queries, mvi)
			if err != nil {
				return nil, selferrors.ClassifyError_ExecResultUnmarshalError.Derived(err)
			}
			result := s.processDomains(req, scores, mvi)

			return result, nil
		},
	})
}

func (s *Service) MedcsProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request]) *pandora_proto.PandoraResponseMessage[model.Response] {
	return s.commonProcess(processConfig{
		ctx:      ctx,
		traceId:  req.Header.TraceId,
		reqModel: req.Payload.Model,
		model:    s.Config.Medcs.Model,
		enabled:  s.Config.Medcs.Enabled,
		validator: func() *errtypes.SelfError {
			return s.ValidateParameters(req)
		},
		processor: func(mvi *model.Version) ([]*model.Result, error) {

			scores, err := s.RunToken(ctx, req.Payload.Queries, mvi)
			if err != nil {
				return nil, selferrors.ClassifyError_ExecResultUnmarshalError.Derived(err)
			}
			result := s.processMedcs(req, scores, mvi)

			return result, nil
		},
	})

}

func (s *Service) EmergencyProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request]) *pandora_proto.PandoraResponseMessage[model.Response] {
	return s.commonProcess(processConfig{
		ctx:      ctx,
		traceId:  req.Header.TraceId,
		model:    s.Config.Emergency.Model,
		reqModel: req.Payload.Model,
		enabled:  s.Config.Emergency.Enabled,
		validator: func() *errtypes.SelfError {
			return s.ValidateParameters(req)
		},
		processor: func(mvi *model.Version) ([]*model.Result, error) {

			return nil, nil
		},
	})
}

func (s *Service) TimelinessProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.TimelinessRequest]) *pandora_proto.PandoraResponseMessage[model.Response] {
	return s.commonProcess(processConfig{
		ctx:      ctx,
		traceId:  req.Header.TraceId,
		model:    s.Config.Timeliness.Model,
		reqModel: req.Payload.Model,
		enabled:  s.Config.Timeliness.Enabled,
		validator: func() *errtypes.SelfError {
			return s.ValidateTimelinessParameters(req)
		},
		processor: func(mvi *model.Version) ([]*model.Result, error) {
			queries := make([]string, len(req.Payload.Texts))
			for i, query := range req.Payload.Queries {
				if _, exists := consts.TimelinessDomainMap[query.Domain]; !exists {
					query.Domain = 0
				}
				queries[i] = fmt.Sprintf("%s %s", consts.TimelinessDomainMap[query.Domain], query.Query)
			}
			req.Payload.TimelinessQueries = queries

			result, err := s.RunToken(ctx, queries, mvi)
			if err != nil {
				return nil, selferrors.ClassifyError_ExecResultUnmarshalError.Derived(err)
			}
			res := s.processTimelinessResults(req, result)

			return res, nil
		},
	})
}

// 处理领域映射
func (s *Service) processDomains(req *pandora_proto.PandoraRequestMessage[model.Request], scores [][]float32, mvi *model.Version) []*model.Result {
	results := make([]*model.Result, len(scores))

	for index, score := range scores {

		domainTemp := make([]*model.Domain, len(score))
		for j := range score {
			domainTemp[j] = &model.Domain{
				Label:  mvi.Lables[j],
				Domain: consts.DomainMap[mvi.Lables[j]],
				Score:  score[j],
			}
		}

		sort.Sort(ByScore(domainTemp))
		for index, domain := range domainTemp {
			domain.Index = index + 1
		}

		if req.Payload.Top <= 0 || req.Payload.Top > len(domainTemp) {
			req.Payload.Top = len(domainTemp)
		}
		domainTemp = domainTemp[:req.Payload.Top]

		results[index] = &model.Result{
			Query:   req.Payload.Queries[index],
			Domains: domainTemp,
		}

	}

	return results
}

func (s *Service) processMedcs(req *pandora_proto.PandoraRequestMessage[model.Request], scores [][]float32, mvi *model.Version) []*model.Result {
	results := make([]*model.Result, len(scores))

	for index, score := range scores {
		domainTemp := make([]*model.Domain, 2)

		predScore := float32(1.0)
		if len(score) == 1 {
			predScore = score[0]
		}
		domainTemp[0] = &model.Domain{
			Label:  mvi.Lables[0],
			Domain: consts.MedcsMap[mvi.Lables[0]],
			Score:  predScore,
		}
		domainTemp[1] = &model.Domain{
			Label:  mvi.Lables[1],
			Domain: consts.MedcsMap[mvi.Lables[1]],
			Score:  1 - predScore,
		}

		sort.Sort(ByScore(domainTemp))
		for index, domain := range domainTemp {
			domain.Index = index + 1
		}

		if req.Payload.Top <= 0 || req.Payload.Top > len(domainTemp) {
			req.Payload.Top = len(domainTemp)
		}

		results[index] = &model.Result{
			Query:   req.Payload.Queries[index],
			Domains: domainTemp[:req.Payload.Top],
		}

	}

	return results
}

type ByScore []*model.Domain

func (a ByScore) Len() int           { return len(a) }
func (a ByScore) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a ByScore) Less(i, j int) bool { return a[i].Score > a[j].Score }

// 处理时效性结果
func (s *Service) processTimelinessResults(req *pandora_proto.PandoraRequestMessage[model.TimelinessRequest], scores [][]float32) []*model.Result {

	// 添加边界检查
	if len(scores) != len(req.Payload.Queries) {
		return nil
	}

	labels := utils.Argmax(scores)
	// 添加容量预分配
	results := make([]*model.Result, len(req.Payload.Queries))

	for i, label := range labels {
		results[i] = &model.Result{
			Query:      req.Payload.Queries[i].Query,
			Score:      scores[i][label],
			Timeliness: consts.LabelMap[label],
			Domains:    nil,
		}
	}

	for i, item := range req.Payload.Texts {
		switch timeFlag := utils.BeforeOrAfterDates(item.Text, item.Domain); timeFlag {
		case 1:
			results[i].Timeliness = consts.TIMELINESS_NORMAL
		case -1:
			results[i].Timeliness = consts.TIMELINESS_WEAK
		case 10:
			results[i].Timeliness = consts.TIMELINESS_STRONG
		}
	}

	return results
}
