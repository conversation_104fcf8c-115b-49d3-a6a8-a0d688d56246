package service

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/model"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_util "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/data_tool"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	token "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

func (s *Service) RunToken(ctx *pandora_context.PandoraContext, queries []string, mvi *model.Version) ([][]float32, error) {

	// 调用assembleToken函数，将req.Payload.Querys转换为token和shape
	// start := time.Now()
	token, shape, err := s.assembleToken(mvi.Token, queries)
	if err != nil {
		return nil, err
	}

	// 获取ASE SDK的实例
	client := goboot.AseSdk().GetInstance(mvi.Model).Request()
	// 发送请求
	resp, err := client.Send(context.Background(), &aseclient.InferReq{
		Inputs: token,
		Shapes: shape,
	})
	if err != nil {
		return nil, err
	}
	// 将响应的payload转换为切片
	if resp.Header.Code != 0 {
		return nil, errors.New("model response code not 0,code:" + strconv.Itoa(int(resp.Header.Code)) + ",msg:" + resp.Header.Success)
	}

	scores := pandora_util.BytesToSlice[float32](resp.Payload.Outputs[mvi.OutputName])
	result := [][]float32{}
	if mvi.Model == "medcs" {
		if len(scores) != len(queries) {
			return nil, errors.New("model result len not match,result len:" + strconv.Itoa(len(scores)))
		}

		scores = pandora_util.Sigmoid(scores)

		for index, _ := range queries {
			temp := make([]float32, 1)
			temp[0] = scores[index]
			result = append(result, temp)
		}
	} else {
		// scores = pandora_util.Sigmoid(scores)
		// 判断scores的长度是否与req.Payload.Querys的长度匹配
		if len(scores) != len(queries)*mvi.ResDim {
			return nil, errors.New("model result len not match,result len:" + strconv.Itoa(len(scores)))
		}

		scores, err = softmax(scores)
		if err != nil {
			return nil, err
		}
		// 遍历req.Payload.Querys，将scores转换为IntentResponseData

		for index, _ := range queries {
			temp := make([]float32, mvi.ResDim)
			start := index * mvi.ResDim
			copy(temp, scores[start:start+mvi.ResDim])
			result = append(result, temp)
		}
	}

	return result, nil

}

// 函数assembleToken用于将查询字符串转换为tokenMap和shapeMap
func (s *Service) assembleToken(tokenName string, queries []string) (map[string][]byte, map[string][]int64, error) {
	// 创建tokenMap和shapeMap
	tokenMap := make(map[string][]byte)
	shapeMap := make(map[string][]int64)

	// 使用goboot.Tokenizer()获取实例，并使用EncodeBatch方法将查询字符串转换为编码
	encodings, err := token.AsyncEncodeBatch[int64](token.G_tokenizer.GetInstance(tokenName), queries, true)
	if err != nil {
		return nil, nil, err
	}

	// 遍历编码，将编码转换为字节数组，并添加到tokenMap中
	for _, encoding := range encodings {
		tokenMap["input_ids"] = append(tokenMap["input_ids"], pandora_util.SliceToBytes(encoding.Ids)...)
		tokenMap["attention_mask"] = append(tokenMap["attention_mask"], pandora_util.SliceToBytes(encoding.Masks)...)
		tokenMap["token_type_ids"] = append(tokenMap["token_type_ids"], pandora_util.SliceToBytes(encoding.Types)...)

	}

	// 计算batchSize，并将shapeMap中的值设置为batchSize和编码的长度
	batchSize := int64(len(queries))
	shapeMap["input_ids"] = []int64{batchSize, int64(len(encodings[0].Ids))}
	shapeMap["attention_mask"] = []int64{batchSize, int64(len(encodings[0].Masks))}
	shapeMap["token_type_ids"] = []int64{batchSize, int64(len(encodings[0].Types))}

	// 返回tokenMap和shapeMap
	return tokenMap, shapeMap, nil
}

// softmax 计算 softmax 概率分布
func softmax(x []float32) ([]float32, error) {
	// 参数检查
	if len(x) == 0 {
		return nil, errors.New("input slice is empty")
	}

	x64 := make([]float64, len(x))
	for i := range x {
		x64[i] = float64(x[i])
	}

	result := make([]float32, len(x))

	// 找出最大值（防止数值溢出）
	maxVal := x64[0]
	for i := 1; i < len(x64); i++ {
		if x64[i] > maxVal {
			maxVal = x64[i]
		}
	}

	// 计算 exp 和总和
	sum := 0.0
	for i := 0; i < len(x64); i++ {
		// 防止数值溢出，先减去最大值
		expVal := math.Exp(x64[i] - maxVal)
		// 检查是否为 NaN 或 Inf
		if math.IsNaN(expVal) || math.IsInf(expVal, 0) {
			return nil, fmt.Errorf("invalid exp value at index %d: %f", i, expVal)
		}
		result[i] = float32(expVal)
		sum += float64(result[i])
	}

	// 检查总和是否为 0
	if sum == 0 {
		return nil, errors.New("sum is zero, cannot normalize")
	}

	// 归一化
	sumFloat32 := float32(sum)
	for i := 0; i < len(x64); i++ {
		result[i] /= sumFloat32
		// 检查结果是否有效
		if math.IsNaN(float64(result[i])) || math.IsInf(float64(result[i]), 0) {
			return nil, fmt.Errorf("invalid result at index %d: %f", i, result[i])
		}
	}

	return result, nil
}

// Sigmoid 计算sigmoid函数值
func Sigmoid(x float32) float32 {
	return 1.0 / (1.0 + float32(math.Exp(-float64(x))))
}
