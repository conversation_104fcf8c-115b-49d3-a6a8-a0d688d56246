package classify

import (
	"fmt"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/service"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	tokenizer_v2 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

// 初始化函数
func Init() error {

	// 解析自定义toml配置块
	modelConfig := &model.ModelConfig{}

	err := goboot.UnmarshalConf(modelConfig)
	if err != nil {
		return err
	}
	if err := goboot.RegisterCustomMultiModule(tokenizer_v2.G_TokenizerFactory); err != nil {
		return fmt.Errorf("register custom module failed, error: %s", err.Error())
	}

	// tokenizer_v2依赖是否加载
	tokenizer_v2.G_tokenizer.Must()

	// 初始化全局服务
	service.GService = &service.Service{}
	// 加载服务配置
	c, err := config.LoadServiceConfig(modelConfig.Config.ConfigPath)
	if err != nil {
		return err
	}
	// 将加载的配置赋值给全局服务
	service.GService.Config = c

	// 注册路由
	service.GService.RegisterRouter()

	return nil
}
