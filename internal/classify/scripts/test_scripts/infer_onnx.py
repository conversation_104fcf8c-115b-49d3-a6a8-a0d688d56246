import os
import time
import json
import numpy as np
import torch
import onnxruntime as ort
from transformers import Bert<PERSON>oken<PERSON>
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import torch.nn.functional as F


class RankingDataset(Dataset):
    def __init__(self, file_path, tokenizer, max_length_limit):
        self.data = []
        self.tokenizer = tokenizer
        self.max_length_limit = max_length_limit
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data = json.loads(line.strip())
                self.data.append(data)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        query = item['query']

        # 编码 query 和 title 对
        encoded_inputs = self.tokenizer(
            query,
            padding='max_length',
            truncation=True,
            max_length=self.max_length_limit,
            return_tensors='pt'
        )

        # 处理编码后的输入
        input_ids = encoded_inputs['input_ids'].squeeze(0)
        attention_mask = encoded_inputs['attention_mask'].squeeze(0)
        token_type_ids = encoded_inputs.get('token_type_ids', None)
        token_type_ids = token_type_ids.squeeze(0) if token_type_ids is not None else None

        print(input_ids.shape)

        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'token_type_ids': token_type_ids,
            'original_data': item
        }


# 自定义collate函数动态统一一个batch内的title数量和序列长度
def custom_collate(batch):
    # 1. 计算当前batch的最大title数量和最大序列长度, 第i个样本的input_ids形状是[num_titles(i), max_length(i)]
    max_length = max(item['input_ids'].size(0) for item in batch)

    # 2. 初始化存储列表
    input_ids_list = []
    attention_mask_list = []
    token_type_ids_list = []
    # scores_list = []
    original_data_list = [item['original_data'] for item in batch]

    # 3. 对每个样本进行填充
    for item in batch:
        input_ids = item['input_ids']
        attention_mask = item['attention_mask']
        token_type_ids = item['token_type_ids']
        # score = item['score']

        # a. 填充序列长度到max_max_length
        padding_length = max_length - input_ids.size(0)
        if padding_length > 0:
            # 填充input_ids
            input_ids = F.pad(input_ids, (0, padding_length), mode='constant', value=0)
            # 填充attention_mask
            attention_mask = F.pad(attention_mask, (0, padding_length), mode='constant', value=0)
            # 填充token_type_ids
            token_type_ids = F.pad(token_type_ids, (0, padding_length), mode='constant', value=0) if token_type_ids is not None else None

        # 4. 添加到列表
        input_ids_list.append(input_ids)
        attention_mask_list.append(attention_mask)
        token_type_ids_list.append(token_type_ids) if token_type_ids is not None else None
        # scores_list.append(score)

    # 5. 堆叠为batch tensor
    input_ids_batch = torch.stack(input_ids_list)
    attention_mask_batch = torch.stack(attention_mask_list)
    token_type_ids_batch = torch.stack(token_type_ids_list) if token_type_ids_list else None
    # scores_batch = torch.stack(scores_list)

    return {
        'input_ids': input_ids_batch,
        'attention_mask': attention_mask_batch,
        'token_type_ids': token_type_ids_batch,
        # 'scores': scores_batch,
        'original_data': original_data_list
    }


# ONNX模型推理函数
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
def inference_onnx(session, eval_dataloader, device, num_classes=2):
    all_true_labels = [] # 存储所有真实标签
    all_pred_labels = [] # 存储所有预测标签
    all_pred_probs = [] # 存储所有预测概率（用于多分类概率分析）

    # 初始化时间统计变量
    total_inference_time = 0.0
    batch_count = 0
    results = []
    
    with torch.no_grad():
        for batch in tqdm(eval_dataloader, desc="Evaluating"):
            input_ids = batch['input_ids'].cpu().numpy()
            attention_mask = batch['attention_mask'].cpu().numpy()
            token_type_ids = batch['token_type_ids'].cpu().numpy() if batch['token_type_ids'] is not None else None
            # scores = batch['scores'].to(device)
            batch_size = input_ids.shape[0]
            original_data = batch['original_data']

            # 记录推理开始时间
            start_time = time.time()
            
            
            # 准备输入字典
            inputs = {
                'input_ids': input_ids,
                'attention_mask': attention_mask
            }
            if token_type_ids is not None:
                inputs['token_type_ids'] = token_type_ids
            
            # ONNX推理
            outputs = session.run(None, inputs)
            logits = outputs[0]
            
            # 记录推理结束时间并计算耗时
            end_time = time.time()
            total_inference_time += (end_time - start_time)
            batch_count += 1

            if num_classes == 2:
                # 二分类：sigmoid+阈值0.5
                # probs = torch.sigmoid(logits)  # [batch_size]
                # preds = (probs >= 0.5).long()   # 大于等于0.5为1，否则为0
                # all_pred_probs.extend(probs.cpu().numpy())
                preds = (logits >= 0).astype(int)   # 大于等于0.5为1，否则为0
                # all_pred_probs.extend(logits.cpu().numpy())
                all_pred_probs.extend(logits)
            else:
                # 多分类：softmax+取最大值索引
                probs = torch.softmax(logits, dim=1)  # [batch_size, num_classes]
                preds = torch.argmax(probs, dim=1)    # [batch_size]
                all_pred_probs.extend(probs.cpu().numpy())
            
            # 将推理结果转换为numpy并添加到原始数据
            for i in range(len(original_data)):
                data = original_data[i].copy()

                true_label = data.get('label')
                all_true_labels.append(true_label)

                pred_label = preds[i].item()
                all_pred_labels.append(pred_label)
                data['pred_label'] = pred_label
                # data['pred_prob'] = float(probs[i].cpu())
                # data['pred_prob'] = float(logits[i].cpu())
                data['pred_prob'] = float(logits[i])

                results.append(data)
    
    # 计算平均推理时间
    avg_inference_time = (total_inference_time / batch_count) * 1000 # 转换为毫秒
    print(f"Average Inference Time: {avg_inference_time:.4f} ms per batch")

    # 转换为numpy数组以便计算统计指标
    y_true = np.array(all_true_labels)
    y_pred = np.array(all_pred_labels)

    # 计算分类指标
    # 1. 准确率（所有类别正确预测的比例）
    accuracy = accuracy_score(y_true, y_pred)
    
    # 2. 精确率、召回率、F1值（支持多分类，默认macro平均）
    # macro：计算每个类别的指标后取算术平均（不考虑类别不平衡）
    # micro：计算全局指标（适合类别不平衡场景）
    precision = precision_score(y_true, y_pred, average='macro', zero_division=0)
    recall = recall_score(y_true, y_pred, average='macro', zero_division=0)
    f1 = f1_score(y_true, y_pred, average='macro', zero_division=0)
    
    # 3. 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)

    # 打印指标
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Precision (macro): {precision:.4f}")
    print(f"Recall (macro): {recall:.4f}")
    print(f"F1 Score (macro): {f1:.4f}")
    print("Confusion Matrix:")
    print(cm)
    
    # （可选）绘制混淆矩阵热力图
    # plt.figure(figsize=(8, 6))
    # sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
    #             xticklabels=[f'Class {i}' for i in range(num_classes)],
    #             yticklabels=[f'Class {i}' for i in range(num_classes)])
    # plt.xlabel('Predicted')
    # plt.ylabel('True')
    # plt.title('Confusion Matrix')
    # cm_path = os.path.join("/data1/yicheng3/Medicine_Classification_Query/Onnx_Tools", 'confusion_matrix.png')
    # plt.savefig(cm_path)
    # print(f"Confusion matrix saved to {cm_path}")
    # plt.close()
    
    return results, {
        "Accuracy": accuracy,
        "Precision": precision,
        "Recall": recall,
        "F1": f1
    }



def main():
    # 数据路径参数
    input_path = '/data1/yicheng3/Medicine_Classification/Data_query_v20250827/test2.json'
    onnx_model_path = '/data1/yicheng3/Medicine_Classification_Query/Onnx_Tools/reranking_model.onnx'
    tokenizer_path = '/data1/yicheng3/Medicine_Classification_Query/model'
    output_file = '/data1/yicheng3/Medicine_Classification_Query/Onnx_Tools/testset_out_onnx.json'
    
    # 推理参数
    batch_size = 1
    max_length = 2048
    num_workers = 1
    
    # 设置设备
    device = 'cuda' if ort.get_device() == 'GPU' else 'CPU'
    print(device)
    
    # 加载Tokenizer
    tokenizer = BertTokenizer.from_pretrained(tokenizer_path)
    
    # 加载数据集
    inference_dataset = RankingDataset(
        input_path, 
        tokenizer, 
        max_length
    )
    
    inference_dataloader = DataLoader(
        inference_dataset,
        batch_size=batch_size,
        num_workers=num_workers,
        pin_memory=True,
        shuffle=False,
        collate_fn=custom_collate
    )

    # 加载ONNX模型
    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider'] if device == 'cuda' else ['CPUExecutionProvider']
    session = ort.InferenceSession(onnx_model_path, providers=providers)

    # 推理
    results, eval_results = inference_onnx(
        session, 
        inference_dataloader, 
        device
    )
    for key, value in eval_results.items():
        print(f"Evaluation {key}: {value:.4f}")
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        for result in results:
            f.write(json.dumps(result, ensure_ascii=False) + '\n')
    print(f"Results saved to {output_file}")


if __name__ == "__main__":
    main()
