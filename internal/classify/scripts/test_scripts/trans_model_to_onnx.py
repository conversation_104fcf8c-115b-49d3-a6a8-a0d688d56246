import os
import torch
import torch.nn as nn
import argparse
import onnx
from transformers import AutoConfig, BertToken<PERSON>, BertModel, ErnieModel


class RankingModel(nn.Module):
    def __init__(self, model_path, num_labels=1):
        super(RankingModel, self).__init__()
        config = AutoConfig.from_pretrained(model_path)
        if config.model_type == "bert":
            self.encoder = BertModel.from_pretrained(model_path)
        elif config.model_type == "ernie":
            self.encoder = ErnieModel.from_pretrained(model_path)
        else:
            raise ValueError(f"Unsupported model type:{config.model_type}")
        self.dropout = nn.Dropout(0.1)
        self.classifier = nn.Linear(self.encoder.config.hidden_size, num_labels)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, input_ids, attention_mask, token_type_ids):
        outputs = self.encoder(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids
        )

        # 提取[CLS]标记输出
        cls_output = outputs.last_hidden_state[:, 0, :]   # [batch_size, hidden_size]
        cls_output = self.dropout(cls_output)             # [batch_size, hidden_size]
        
        # 对每个标题独立分类
        logits = self.classifier(cls_output).squeeze(-1)  # [batch_size]
        
        # 将logits映射到0-1之间
        # probs = self.sigmoid(logits)                    # [batch_size]

        return logits.float()                                     # [batch_size]


def export_model_to_onnx(model, args, device):

    # 设置输入形状
    num_docs = args.batch_size
    max_seq_length = args.max_length
    
    # 构造输入张量
    input_ids = torch.randint(0, 10000, (num_docs, max_seq_length), dtype=torch.long).to(device)
    attention_mask = torch.ones((num_docs, max_seq_length), dtype=torch.long).to(device)
    token_type_ids = torch.zeros((num_docs, max_seq_length), dtype=torch.long).to(device)
    
    # 定义动态轴, 支持可变batch_size、max_num_titles和sequence_length
    dynamic_axes = {
        'input_ids': {0: 'num_docs', 1: 'sequence_length'},
        'attention_mask': {0: 'num_docs', 1: 'sequence_length'},
        'token_type_ids': {0: 'num_docs', 1: 'sequence_length'},
        'logits': {0: 'num_docs'}
    }

    # 导出模型
    torch.onnx.export(
        model,
        f=args.output_path,
        args=(input_ids, attention_mask, token_type_ids),
        input_names=['input_ids', 'attention_mask', 'token_type_ids'],
        output_names=['logits'],
        dynamic_axes=dynamic_axes,
        opset_version=14,
        do_constant_folding=True,
        verbose=False
    )
    
    # 验证导出的ONNX模型
    try:
        onnx_model = onnx.load(args.output_path)
        onnx.checker.check_model(onnx_model)
        print(f"ONNX model verification succeeded!")
    except Exception as e:
        print(f"ONNX model verification failed: {e}")


def main():
    parser = argparse.ArgumentParser(description='Convert PyTorch Ranking Model to ONNX')

    # 模型相关参数
    parser.add_argument('--model_path', type=str, default='/data/bak/wfliu3/lynxiao/lynxiao-ai-search/internal/classify/scripts/epoch_27_Accuracy_0.9950', help='Path to the directory containing PyTorch model weights')
    parser.add_argument('--output_path', type=str, default='/data/bak/wfliu3/lynxiao/lynxiao-ai-search/internal/classify/scripts/epoch_27_Accuracy_0.9950/Classfication_model.onnx', help='Path to save the ONNX model')
    parser.add_argument('--batch_size', type=int, default=1, help='Batch size')
    parser.add_argument('--max_length', type=int, default=64, help='Maximum sequence length')

    # GPU参数
    parser.add_argument('--use_cuda', type=bool, default=True, help='Whether to use CUDA')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device('cuda' if args.use_cuda and torch.cuda.is_available() else 'cpu')
    
    # 初始化模型
    model = RankingModel(args.model_path)
    
    # 加载模型权重
    model_path = os.path.join(args.model_path, 'pytorch_model.bin')
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"File not found: {model_path}")
    
    # 加载模型权重
    # model.load_state_dict(torch.load(model_path, map_location=device))
    state_dict = torch.load(model_path, map_location=torch.device('cpu'))
    
    # 处理DataParallel训练的模型
    if any(key.startswith('module.') for key in state_dict.keys()):
        from collections import OrderedDict
        new_state_dict = OrderedDict()
        for k, v in state_dict.items():
            name = k[7:] # 移除module.前缀
            new_state_dict[name] = v
        state_dict = new_state_dict
    
    model.load_state_dict(state_dict)
    
    # 半精度保存
    model = model.half().to(device)

    model.eval()
    
    # 创建输出目录
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    
    # 导出模型
    print("=======")
    export_model_to_onnx(model, args, device)
    print(f"Model has been successfully exported to: {args.output_path}")


if __name__ == "__main__":
    main()