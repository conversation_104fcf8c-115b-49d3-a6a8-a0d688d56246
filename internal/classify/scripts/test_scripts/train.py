import os
import time
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.optim import AdamW
from transformers import AutoConfig, Bert<PERSON>okenizer, BertModel, ErnieModel
from transformers import get_linear_schedule_with_warmup
from torch.amp import autocast, GradScaler
from tqdm import tqdm
import argparse
from collections import Counter


# 设置随机种子以确保结果可复现
def set_seed(seed):
    os.environ['PYTHONHASHSEED'] = str(seed)        # Python 哈希种子(确保字典等数据结构的遍历顺序一致)
    np.random.seed(seed)                            # NumPy 随机数生成器种子
    torch.manual_seed(seed)                         # CPU 随机数生成器种子
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)            # GPU 随机数生成器种子
        # torch.backends.cudnn.deterministic = True # cuDNN 的确定性算法, 默认False
        # torch.backends.cudnn.benchmark = True     # cuDNN 的自动调优机制, 默认False


# 自定义数据集类
class RankingDataset(Dataset):
    def __init__(self, file_path, tokenizer, max_length_limit):
        self.data = []
        self.tokenizer = tokenizer
        self.max_length_limit = max_length_limit
        self.labels = [] # 用于统计所有标签分布
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data = json.loads(line.strip())
                self.data.append(data)
                self.labels.append(data['label'])
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        query = item['query']
        label = item['label']

        # label转换为tensor格式
        label = torch.tensor(label, dtype=torch.float)

        # 编码
        encoded_inputs = self.tokenizer(
            query,
            padding=False,
            truncation=True,
            max_length=self.max_length_limit,
            return_tensors='pt'
        )
        
        # print(encoded_inputs)
        #      input_ids: [ 101[CLS], 3389, 6117, 2382, 6226, 7444, 6206, 3800, 2692,  763,  784,  720,  102[SEP], 6117, 2382, 6226, 3466, 3389, 6206, 3800, 2692,  784,  720,  102[SEP],    0,    0,    0,    0,    0,    0,    0]
        # attention_mask: [        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,    1,    1,         1,     1,   1,    1,    1,    1,    1,    1,     1,    1,   1,         1,    0,    0,    0,    0,    0,    0,    0]
        # token_type_ids: [        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,         0,     1,   1,    1,    1,    1,    1,    1,     1,    1,   1,         1,    0,    0,    0,    0,    0,    0,    0]
        
        # 处理编码后的输入, 维度:[max_length]
        input_ids = encoded_inputs['input_ids'].squeeze(0)
        attention_mask = encoded_inputs['attention_mask'].squeeze(0)
        token_type_ids = encoded_inputs.get('token_type_ids', None)
        token_type_ids = token_type_ids.squeeze(0) if token_type_ids is not None else None

        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'token_type_ids': token_type_ids,
            'label': label,
        }
    
    # 获取标签分布
    def get_label_distribution(self):
        counter = Counter(self.labels)
        total = sum(counter.values())
        # 计算类别权重 (1/类别频率)
        weights = {label: total / count for label, count in counter.items()}
        return weights


# 自定义collate函数动态统一一个batch内的序列长度
def custom_collate(batch):
    # 1. 计算当前batch的最大序列长度, 第i个样本的input_ids形状是[num_queries(i), max_length(i)]
    max_length = max(item['input_ids'].size(0) for item in batch)

    # 2. 初始化存储列表
    input_ids_list = []
    attention_mask_list = []
    token_type_ids_list = []
    label_list = []

    # 3. 对每个样本进行填充
    for item in batch:
        input_ids = item['input_ids']
        attention_mask = item['attention_mask']
        token_type_ids = item['token_type_ids']
        label = item['label']

        # a. 填充序列长度到max_max_length
        padding_length = max_length - input_ids.size(0)
        if padding_length > 0:
            # 填充input_ids
            input_ids = F.pad(input_ids, (0, padding_length), mode='constant', value=0)
            # 填充attention_mask
            attention_mask = F.pad(attention_mask, (0, padding_length), mode='constant', value=0)
            # 填充token_type_ids
            token_type_ids = F.pad(token_type_ids, (0, padding_length), mode='constant', value=0) if token_type_ids is not None else None

        # 4. 添加到列表
        input_ids_list.append(input_ids)
        attention_mask_list.append(attention_mask)
        token_type_ids_list.append(token_type_ids) if token_type_ids is not None else None
        label_list.append(label)

    # 5. 堆叠为batch tensor
    input_ids_batch = torch.stack(input_ids_list)
    attention_mask_batch = torch.stack(attention_mask_list)
    token_type_ids_batch = torch.stack(token_type_ids_list) if token_type_ids_list else None
    label_batch = torch.stack(label_list)

    return {
        'input_ids': input_ids_batch,
        'attention_mask': attention_mask_batch,
        'token_type_ids': token_type_ids_batch,
        'label': label_batch,
    }


# 自定义排序模型
class RankingModel(nn.Module):
    def __init__(self, model_path, num_labels=1):
        super(RankingModel, self).__init__()
        config = AutoConfig.from_pretrained(model_path)
        if config.model_type == "bert":
            self.encoder = BertModel.from_pretrained(model_path)
        elif config.model_type == "ernie":
            self.encoder = ErnieModel.from_pretrained(model_path)
        else:
            raise ValueError(f"Unsupported model type: {config.model_type}")
        self.dropout = nn.Dropout(0.1)
        self.classifier = nn.Linear(self.encoder.config.hidden_size, num_labels)
        # self.sigmoid = nn.Sigmoid()
    
    def forward(self, input_ids, attention_mask, token_type_ids):
        # 编码器输出
        outputs = self.encoder(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids
        )

        # 直接提取[CLS]标记的输出作为文本表示
        cls_output = outputs.last_hidden_state[:, 0, :]                           # [batch_size, hidden_size], batch_size = batch_size / GPUs (真正的 batch_size / GPU 数目)
        cls_output = self.dropout(cls_output)                                     # [batch_size, hidden_size]

        # 对每个标题独立分类
        logits = self.classifier(cls_output)                                      # [batch_size, 1]
        logits = logits.squeeze(-1)                                               # [batch_size]
         
        # 将logits映射到0-1之间
        # probs = self.sigmoid(logits)                                            # [batch_size]

        return logits                                                             # [batch_size]


# 训练
def train(model, train_dataloader, optimizer, scheduler, epoch, device, args, scaler, class_weights):
    model.train()
    total_loss = 0
    
    progress_bar = tqdm(enumerate(train_dataloader), total=len(train_dataloader))
    for step, batch in progress_bar:
        input_ids = batch['input_ids'].to(device)           # [batch_size, max_length]
        attention_mask = batch['attention_mask'].to(device) # [batch_size, max_length]
        token_type_ids = batch['token_type_ids'].to(device) if batch['token_type_ids'] is not None else None # [batch_size, max_length]
        label = batch['label'].to(device)                   # [batch_size]
        
        # 清除优化器中的梯度累积
        optimizer.zero_grad()
        
        # 是否启用FP16
        if args.fp16:
            # 前向传播 - 使用autocast上下文管理器启用FP16
            with autocast(device_type='cuda'):
                logits = model(input_ids, attention_mask, token_type_ids) # [batch_size]

                # 二分类BCE损失
                # loss = nn.BCEWithLogitsLoss()(logits, label.float())
                
                # 二分类加权BCE损失
                weight_tensor = torch.tensor([class_weights[0], class_weights[1]]).to(device)
                loss = nn.BCEWithLogitsLoss(pos_weight=weight_tensor[1]/weight_tensor[0])(logits, label.float()) # pos_weight参数用于设置正类的权重, 公式: loss = -w * [y * log(sigmoid(x)) + (1-y) * log(1-sigmoid(x))]
                
                # 多分类CE损失
                # loss = nn.CrossEntropyLoss()(logits, label)

                total_loss += loss.item()
            
            # 反向传播 - 使用GradScaler处理FP16梯度
            scaler.scale(loss).backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

            # 更新优化器参数 - 通过scaler.step()应用梯度
            scaler.step(optimizer)
            # 更新scaler
            scaler.update()
        
        else:
            logits = model(input_ids, attention_mask, token_type_ids) # [batch_size]
 
            # 二分类BCE损失
            # loss = nn.BCEWithLogitsLoss()(logits, label.float())
            
            # 二分类加权BCE损失
            weight_tensor = torch.tensor([class_weights[0], class_weights[1]]).to(device)
            loss = nn.BCEWithLogitsLoss(pos_weight=weight_tensor[1]/weight_tensor[0])(logits, label.float()) # pos_weight参数用于设置正类的权重, 公式: loss = -w * [y * log(sigmoid(x)) + (1-y) * log(1-sigmoid(x))]
            
            # 多分类CE损失
            # loss = nn.CrossEntropyLoss()(logits, label)

            total_loss += loss.item()
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            # 更新优化器参数
            optimizer.step()
        
        # 学习率调度器更新
        scheduler.step()
        
        progress_bar.set_description(f"Epoch {epoch}, Loss: {loss.item():.4f}")
    
    avg_loss = total_loss / len(train_dataloader)
    return avg_loss


# 验证
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
def evaluate(model, eval_dataloader, device, args, num_classes):
    model.eval()
    all_true_labels = [] # 存储所有真实标签
    all_pred_labels = [] # 存储所有预测标签
    all_pred_probs = []  # 存储所有预测概率

    # 初始化时间统计变量
    total_inference_time = 0.0
    batch_count = 0
    
    with torch.no_grad():
        for batch in tqdm(eval_dataloader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            token_type_ids = batch['token_type_ids'].to(device) if batch['token_type_ids'] is not None else None
            label = batch['label'].to(device)

            # 记录推理开始时间
            start_time = time.time()
            
            # 是否启用FP16
            if args.fp16:
                with autocast(device_type='cuda'):
                    logits = model(input_ids, attention_mask, token_type_ids)
            else:
                logits = model(input_ids, attention_mask, token_type_ids)
            
            # 记录推理结束时间并计算耗时
            end_time = time.time()
            total_inference_time += (end_time - start_time)
            batch_count += 1

            # 推理结果转换为预测标签
            if num_classes == 2:
                # 二分类: sigmoid+阈值(阈值设为0)
                probs = torch.sigmoid(logits)
                preds = (probs >= 0.5).long()
                # preds = (logits >= 0).long()
                all_pred_probs.extend(probs.cpu().numpy())
                # all_pred_probs.extend(logits.cpu().numpy())
            else:
                # 多分类: softmax+取最大值索引
                probs = torch.softmax(logits, dim=1)
                preds = torch.argmax(probs, dim=1)
                all_pred_probs.extend(probs.cpu().numpy())

            # 收集标签
            all_true_labels.extend(label.cpu().numpy())
            all_pred_labels.extend(preds.cpu().numpy())
    
    # 计算平均推理时间
    avg_inference_time = (total_inference_time / batch_count) * 1000 # 转换为毫秒
    print(f"Average Inference Time: {avg_inference_time:.4f} ms per batch")

    # 转换为numpy数组以便计算统计指标
    y_true = np.array(all_true_labels)
    y_pred = np.array(all_pred_labels)
    
    # 计算分类指标
    # 1. 准确率
    accuracy = accuracy_score(y_true, y_pred)
    
    # 2. 精确率、召回率、F1值(默认macro平均)
    # macro: 计算每个类别的指标后取算术平均(不考虑类别不平衡)
    # micro: 计算全局指标(适合类别不平衡场景)
    precision = precision_score(y_true, y_pred, average='macro', zero_division=0)
    recall = recall_score(y_true, y_pred, average='macro', zero_division=0)
    f1 = f1_score(y_true, y_pred, average='macro', zero_division=0)
    
    # 3. 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)

    # 打印指标
    # print(f"Accuracy: {accuracy:.4f}")
    # print(f"Precision (macro): {precision:.4f}")
    # print(f"Recall (macro): {recall:.4f}")
    # print(f"F1 Score (macro): {f1:.4f}")
    # print("Confusion Matrix:")
    print(cm)
    
    # (可选) 绘制混淆矩阵热力图
    if args.save:
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                    xticklabels=[f'Class {i}' for i in range(num_classes)],
                    yticklabels=[f'Class {i}' for i in range(num_classes)])
        plt.xlabel('Predicted')
        plt.ylabel('True')
        plt.title('Confusion Matrix')
        cm_path = os.path.join(args.output_dir, 'confusion_matrix.png')
        plt.savefig(cm_path)
        # print(f"Confusion matrix saved to {cm_path}")
        plt.close()

    return {
        "Accuracy": accuracy,
        "Precision": precision,
        "Recall": recall,
        "F1": f1
    }


def main():
    parser = argparse.ArgumentParser(description='Classification Model Training')
    
    # 数据路径参数
    parser.add_argument('--train_path', type=str, default="/data1/yicheng3/Medicine_Classification_Query_Dataset/v20250826/train.json", help='Path to training data')
    parser.add_argument('--eval_path', type=str, default="/data1/yicheng3/Medicine_Classification_Query_Dataset/v20250826/test.json", help='Path to evaluation data')
    parser.add_argument('--model_path', type=str, default="/data1/yicheng3/Model_Hub/ernie_nano", help='Path to Pre trained model')
    parser.add_argument('--output_dir', type=str, default='/data1/yicheng3/Medicine_Classification_Query/checkpoint/ernie_nano', help='Model output directory')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=20, help='Number of training epochs')
    parser.add_argument('--train_batch_size', type=int, default=16, help='Train batch size')
    parser.add_argument('--eval_batch_size', type=int, default=1, help='Evaluation batch size')
    parser.add_argument('--max_length', type=int, default=64, help='Maximum sequence length')
    parser.add_argument('--learning_rate', type=float, default=1e-5, help='Learning rate')
    parser.add_argument('--warmup_steps', type=int, default=0, help='Warmup steps for scheduler')
    parser.add_argument('--num_classes', type=int, default=2, help='Number of classes (for classification)')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--save', type=bool, default=True, help='Whether to save model')
    
    # FP16精度
    parser.add_argument('--fp16', type=bool, default=True, help='Whether to use FP16 mixed precision training')
    
    # GPU参数
    parser.add_argument('--gpu_ids', type=str, default='0,1', help='GPU ids to use, e.g., "0,1,2" for multiple GPUs')
    parser.add_argument('--use_cuda', type=bool, default=True, help='Whether to use CUDA')

    # CPU参数
    parser.add_argument('--num_workers', type=int, default=8, help='Number of worker processes for data loading')
    
    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 解析GPU ID
    gpu_ids = [int(gpu) for gpu in args.gpu_ids.split(',')] if args.gpu_ids else []
    args.gpu_ids = gpu_ids
    
    # 设置设备
    device = torch.device('cuda' if args.use_cuda and torch.cuda.is_available() else 'cpu')
    if args.use_cuda and torch.cuda.is_available():
        torch.cuda.set_device(gpu_ids[0])
        print(f"Using GPUs: {gpu_ids}")
    else:
        print("Using CPU")
    
    # 加载tokenizer
    tokenizer = BertTokenizer.from_pretrained(args.model_path)
    
    # 加载数据集
    train_dataset = RankingDataset(
        args.train_path, 
        tokenizer,
        args.max_length
    )

    label_distribution = train_dataset.get_label_distribution()
    # print(f"Label distribution: {label_distribution}")
    
    # 计算类别权重
    class_weights = {0: 1.0, 1: 1.0} # 初始化权重
    if 0 in label_distribution and 1 in label_distribution:
        # 计算权重使两类样本的有效数量相等
        class_weights[0] = label_distribution[1] / (label_distribution[0] + label_distribution[1])
        class_weights[1] = label_distribution[0] / (label_distribution[0] + label_distribution[1])
    print(f"Class weights: {class_weights}")

    eval_dataset = RankingDataset(
        args.eval_path, 
        tokenizer,
        args.max_length
    )
    
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=args.train_batch_size,
        num_workers=args.num_workers,
        pin_memory=True,
        shuffle=False,
        collate_fn=custom_collate
    )
    
    eval_dataloader = DataLoader(
        eval_dataset,
        batch_size=args.eval_batch_size,
        num_workers=args.num_workers,
        pin_memory=True,
        shuffle=False,
        collate_fn=custom_collate
    )
    
    # 初始化模型
    model = RankingModel(args.model_path)
    
    # 数据并行设置
    if args.use_cuda and len(gpu_ids) > 1:
        model = torch.nn.DataParallel(model, device_ids=gpu_ids)
    model = model.to(device)
    
    # 优化器和学习率调度器
    no_decay = ['bias', 'LayerNorm.weight'] # 不需要权重衰减的参数
    optimizer_grouped_parameters = [
        # 应用权重衰减的参数
        {'params': [p for n, p in model.named_parameters() if not any(nd in n for nd in no_decay)], 'weight_decay': 0.01},
        # 不应用权重衰减的参数
        {'params': [p for n, p in model.named_parameters() if any(nd in n for nd in no_decay)], 'weight_decay': 0.0}
    ]

    optimizer = AdamW(optimizer_grouped_parameters, lr=args.learning_rate)
    total_steps = len(train_dataloader) * args.epochs
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=args.warmup_steps,
        num_training_steps=total_steps
    )
    
    # 初始化FP16
    scaler = GradScaler() if args.fp16 else None
    
    # 检查输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 迭代训练
    for epoch in range(args.epochs):
        print(f"======= Epoch {epoch+1}/{args.epochs} =======")

        # 当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        print(f"Current Learning Rate: {current_lr:.8f}")
        
        # 训练
        train_loss = train(model, train_dataloader, optimizer, scheduler, epoch+1, device, args, scaler, class_weights)
        print(f"Epoch {epoch+1}, Train Loss: {train_loss:.4f}")
        
        # 测试
        eval_results = evaluate(model, eval_dataloader, device, args, num_classes=args.num_classes)
        for key, value in eval_results.items():
            print(f"Epoch {epoch+1} Evaluation {key}: {value:.4f}")

        # 保存模型
        if args.save:
            # 创建保存目录
            output_dir = os.path.join(args.output_dir, f"epoch_{epoch+1}_Accuracy_{eval_results['Accuracy']:.4f}")
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存模型权重
            if isinstance(model, torch.nn.DataParallel):
                torch.save(model.module.state_dict(), os.path.join(output_dir, "pytorch_model.bin"))
            else:
                torch.save(model.state_dict(), os.path.join(output_dir, "pytorch_model.bin"))
            
            # 保存模型配置
            if hasattr(model, 'config'):
                model.config.save_pretrained(output_dir)
            else:           
                config = AutoConfig.from_pretrained(args.model_path)
                config.save_pretrained(output_dir)      
            
            # 保存tokenizer
            if tokenizer is not None:
                tokenizer.save_pretrained(output_dir)
            
            # # 保存训练参数
            # with open(os.path.join(output_dir, "training_args.txt"), "w") as f:
            #     f.write(str(args))
            
            print(f"Models saved to {output_dir}")


if __name__ == "__main__":
    main()
