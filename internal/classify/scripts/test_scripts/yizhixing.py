import json
import requests


url = "http://*************:48083/domain/api/v2/medcs"


data = {
  "header": {
    "traceId": "1753769103916"
  },
  "parameter": {
    "id": "6881fad4518cff8833073d7a",
    
    "current.workflow.nodeCode": "ae64f67520"
  },
  "payload": {
    "texts": [],
    "model": "v20250829",
    "topk": 2
  }
}


lines = open("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/internal/classify/scripts/data/eval_output.json","r", encoding="utf-8").readlines()
fp_w = open("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/internal/classify/scripts/data/eval_output_api.json","w", encoding="utf-8")
for line in lines:
    l = json.loads(line)
    data['payload']['texts'] = [json.loads(line)["query"]]
    response = requests.post(url, json=data)
    label = response.json()["payload"]["results"][0]["domains"][0]["label"]
    if label == "西医":
        api_res = 1
    else:
        api_res = 0
    
    l['api_res'] = api_res
    if l['pred_label'] == api_res:
        l['api_res_correct'] = True
    else:
        l['api_res_correct'] = False
    fp_w.write(json.dumps(l, ensure_ascii=False) + '\n')