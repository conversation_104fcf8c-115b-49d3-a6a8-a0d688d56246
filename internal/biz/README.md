# Biz 业务模块文档

## 概述

`internal/biz` 是 lynxiao-ai-search 项目的核心业务模块，负责处理搜索相关的业务逻辑。该模块采用分层架构设计，提供高性能的并发搜索服务。

## 架构设计

### 目录结构

```
internal/biz/
├── api.go              # 模块入口和初始化
├── config/             # 配置管理层
│   └── config.go      # 配置加载和验证
├── consts/             # 常量定义层
│   └── consts.go      # 业务常量和错误信息
├── model/              # 数据模型层
│   ├── biz.go         # 业务数据结构
│   ├── config.go      # 配置数据结构
│   ├── request.go     # 请求数据结构
│   └── response.go    # 响应数据结构
├── service/            # 业务逻辑层
│   └── service.go     # 核心业务逻辑
└── utils/              # 工具层
    └── utils.go       # HTTP 请求工具
```

### 架构层次

1.  **API 层** (`api.go`)：模块入口，负责初始化和配置加载
2.  **服务层** (`service/`)：核心业务逻辑，处理搜索请求
3.  **模型层** (`model/`)：数据结构定义，包含请求/响应模型
4.  **配置层** (`config/`)：配置文件加载和验证
5.  **工具层** (`utils/`)：HTTP 认证和请求工具
6.  **常量层** (`consts/`)：常量定义和错误消息

## 性能优化

### 1. JSON 序列化优化
- 使用 `sonic` 替代标准库 `encoding/json`
- 性能提升约 2-3倍

### 2. 并发控制优化
- 使用 `golang.org/x/sync/errgroup` 替代 `sync.WaitGroup`
- 限制并发数量为 10，防止系统过载
- 支持上下文取消，避免资源泄漏

### 3. 内存分配优化
- 使用 `make` 预分配切片容量
- 避免不必要的变量复制
- 循环中使用索引而非值拷贝

### 4. 网络请求优化
- **复用HTTP连接**：使用全局共享的 `http.Client` 实例，复用TCP连接，降低延迟并避免端口耗尽。
- **上下文传递**：支持上下文传递，实现请求超时控制。
- **错误包装**：改进错误处理，使用 `fmt.Errorf` 包装错误，提供更丰富的上下文信息。

## 核心功能

### 1. 配置管理
- 支持 JSON 配置文件加载
- 配置验证和默认值设置
- 文件存在性和格式检查

### 2. 搜索处理
- 多查询并发处理
- 超时控制和错误处理
- 结果聚合和格式化

### 3. HTTP 认证
- HMAC-SHA256 签名认证
- 自动请求头生成
- 支持上下文传递

## 使用说明

### 初始化

```go
err := biz.Init()
if err != nil {
    log.Fatal("Failed to initialize biz module:", err)
}
```

### 配置文件格式

```json
{
    "host": "https://api.example.com",
    "appId": "your_app_id",
    "apiKey": "your_api_key",
    "apiSecret": "your_api_secret",
    "timeout": 5000,
    "defaultPipeline": "pl_map_agg_search_medical",
    "httpClientTimeout": 10
}
```

### API 端点

- `POST /biz/api/v2` - 新版本搜索接口
- `POST /biz-search/api/v2` - 兼容旧版本搜索接口

### 请求格式

```json
{
    "header": {
        "traceId": "feng-test",
        "appid": "appid"
    },
    "payload": {
        "query": [
            "高血压的症状有哪些"
        ],
        "limit": 5,
        "full_text": true,
        "open_rerank": true,
        "highlight": false,
        "crawler": false,
        "business": "lynxiao-flow",
        "pipeline": "pl_map_agg_search_medical"
    }
}
```

### 响应格式

```json
{
    "header": {
        "code": 0,
        "success": "success",
        "traceId": "feng-test",
        "appId": "appid",
        "skynet-tlb-service-tag-selector": ""
    },
    "payload": {
        "results": [
            {
                "query": "搜索关键词",
                "code": "0",
                "msg": "success",
                "data": [
                    {
                        "title": "标题",
                        "summary": "摘要",
                        "content": "内容",
                        "url": "链接",
                        "score": 0.85
                    }
                ]
            }
        ]
    }
}
```

## 错误处理

### 常见错误码

- `CommonError_InvalidInput`: 输入参数无效
- `CommonError_InternalError`: 内部服务错误
- `BizSearchError_Timeout`: 搜索超时
- `BizSearchError_JsonMarshal`: JSON 序列化错误
- `BizSearchError_RequestFailed`: 请求失败
- `BizSearchError_CalcFailed`: 计算失败
- `BizSearchError_ResponseDecode`: 响应解析错误
- `BizSearchError_CodeFailed`: 业务逻辑错误

### 错误处理策略

1.  **超时处理**：设置合理的超时时间，避免长时间等待
2.  **重试机制**：对于网络错误，可以考虑重试
3.  **降级处理**：在服务不可用时，返回缓存结果或默认结果
4.  **日志记录**：记录详细的错误日志，便于问题排查

## 监控指标

### 性能指标

- **响应时间**：平均响应时间应小于 1000ms
- **并发数**：最大并发请求数为 10
- **成功率**：请求成功率应大于 99%
- **错误率**：错误率应小于 1%

### 监控方法

- 使用 Pandora 框架的 Span 进行链路追踪
- 记录请求和响应日志
- 监控 HTTP 状态码分布
- 统计各种错误类型的发生频率

## 最佳实践

### 1. 配置管理

```go
// 推荐：使用环境变量或配置中心
configPath := os.Getenv("BIZ_CONFIG_PATH")
if configPath == "" {
    configPath = "/etc/biz/config.json"
}
```

### 2. 错误处理

```go
// 推荐：使用包装错误，提供更多上下文信息
if err != nil {
    return fmt.Errorf("failed to process search request: %w", err)
}
```

### 3. 并发控制

```go
// 推荐：使用 errgroup 进行并发控制
g, ctx := errgroup.WithContext(context.Background())
g.SetLimit(10) // 限制并发数
```

### 4. 资源管理

```go
// 推荐：使用 defer 确保资源释放
defer func() {
    if resp != nil {
        resp.Body.Close()
    }
}()
```

## 部署和运维

### 环境变量

- `BIZ_CONFIG_PATH`: 配置文件路径
- `BIZ_LOG_LEVEL`: 日志级别
- `BIZ_MAX_CONCURRENT`: 最大并发数

### 健康检查

```bash
# 检查服务状态
curl -X POST http://localhost:8080/biz/api/v2 \
  -H "Content-Type: application/json" \
  -d '{"query": ["test"]}'
```

### 性能调优

1.  **并发数调整**：根据服务器性能调整 `MaxConcurrentRequests` 常量。
2.  **超时时间**：根据网络延迟和下游服务性能，在配置文件中调整 `timeout` (服务级超时) 和 `httpClientTimeout` (客户端超时)。
3.  **内存优化**：定期监控内存使用情况。
4.  **连接池**：内部已实现全局HTTP连接池，在 `utils` 包中初始化，无需额外配置。

## 故障排查

### 常见问题

1.  **配置文件找不到**
   - 检查配置文件路径是否正确
   - 确认文件权限设置

2.  **请求超时**
   - 检查网络连接
   - 调整配置文件中的 `timeout` 和 `httpClientTimeout`

3.  **内存泄漏**
   - 检查 goroutine 是否正常退出
   - 确认 HTTP 连接是否正确关闭

4.  **认证失败**
   - 检查 API 密钥是否正确
   - 确认时间同步是否正确

### 日志分析

```bash
# 查看错误日志
grep "ERROR" /var/log/biz.log

# 统计错误类型
grep "ERROR" /var/log/biz.log | awk '{print $5}' | sort | uniq -c
```

## 版本历史

### v1.3.0 (当前版本)
- 优化HTTP客户端使用，改为全局共享实例，提升网络性能。
- 移除硬编码的业务逻辑，改为动态构建，提高可维护性。
- 将HTTP客户端超时时间移入配置文件，增强灵活性。
- 重构服务层代码，分离默认值设置与反序列化逻辑。

### v1.2.0
- 使用 sonic 优化 JSON 序列化性能
- 添加并发控制，防止系统过载
- 改进错误处理和日志记录
- 支持上下文传递和请求取消
- 重构常量定义，提高代码可维护性

### v1.1.0
- 添加配置验证功能
- 支持多种配置格式
- 改进 HTTP 认证机制

### v1.0.0
- 基础搜索功能
- HTTP API 接口
- 基本错误处理

## 贡献指南

1.  遵循 Go 语言编码规范
2.  添加充分的单元测试
3.  更新相关文档
4.  确保向后兼容性
5.  进行性能测试

## 许可证

本项目采用内部许可证，仅供公司内部使用。
