package utils

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

var sharedHttpClient *http.Client

// InitHttpClient 初始化一个可复用的http客户端
func InitHttpClient(timeout int) {
	sharedHttpClient = &http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			IdleConnTimeout:     90 * time.Second,
		},
		Timeout: time.Duration(timeout) * time.Second,
	}
}

// 构建认证请求URL
func buildAuthRequestURL(requestURL string, method string, apiKey string, apiSecret string) (string, string, error) {
	// 解析请求URL
	u, err := url.ParseRequestURI(requestURL)
	if err != nil {
		return "", "", err
	}

	// 格式化当前时间
	date := formatDateTime(time.Now().UTC())
	// 构建签名原始字符串
	signatureOrigin := fmt.Sprintf("host: %s\ndate: %s\n%s %s HTTP/1.1", u.Hostname(), date, method, u.RequestURI())
	// 创建HMAC-SHA256哈希对象
	h := hmac.New(sha256.New, []byte(apiSecret))
	// 将签名原始字符串写入哈希对象
	if _, err := h.Write([]byte(signatureOrigin)); err != nil {
		return "", "", err
	}
	// 计算哈希值
	signatureSha := h.Sum(nil)
	// 将哈希值转换为Base64编码
	signatureShaBase64 := base64.StdEncoding.EncodeToString(signatureSha)

	// 构建认证原始字符串
	authorizationOrigin := fmt.Sprintf(`api_key="%s", algorithm="%s", headers="%s", signature="%s"`, apiKey, "hmac-sha256", "host date request-line", signatureShaBase64)
	// 将认证原始字符串转换为Base64编码
	authorizationBase64 := base64.StdEncoding.EncodeToString([]byte(authorizationOrigin))

	// 构建请求参数
	values := url.Values{}
	values.Set("host", u.Hostname())
	values.Set("date", date)
	values.Set("authorization", authorizationBase64)

	// 返回带认证参数的请求URL和主机名
	return requestURL + "?" + values.Encode(), u.Hostname(), nil
}

// 格式化时间
func formatDateTime(t time.Time) string {
	return t.Format("Mon, 02 Jan 2006 15:04:05 GMT") // RFC1123 format
}

// 函数Prepare用于准备请求URL、请求数据、请求方法、应用ID、API密钥和API密钥
func Prepare(requestURL string, requestData []byte, method string, appID string, apiKey string, apiSecret string) (string, string, map[string]string, error) {
	// 调用buildAuthRequestURL函数构建授权请求URL
	authRequestURL, hostName, err := buildAuthRequestURL(requestURL, method, apiKey, apiSecret)
	if err != nil {
		return "", "", nil, fmt.Errorf("Error building auth request URL: %w", err)
	}

	// 构建请求头
	headers := map[string]string{
		"Content-Type": "application/json",
		"Host":         hostName,
		"App-ID":       appID,
	}

	// 返回授权请求URL、请求数据和请求头
	return authRequestURL, string(requestData), headers, nil
}

// ExecuteWithContext 函数用于执行带有上下文的 HTTP 请求，并返回响应结果
func ExecuteWithContext(ctx context.Context, traceId string, authRequestURL string, data string, headers map[string]string) ([]byte, error) {
	// 创建带有上下文的 HTTP 请求
	req, err := http.NewRequestWithContext(ctx, "POST", authRequestURL, strings.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	// 发送 HTTP 请求
	resp, err := sharedHttpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查 HTTP 响应状态码，如果不是 200，则返回错误
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// 读取 HTTP 响应体
	results, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return results, nil
}

// Execute 函数用于执行 HTTP 请求，并返回响应结果
func Execute(traceId string, authRequestURL string, data string, headers map[string]string) ([]byte, error) {
	return ExecuteWithContext(context.Background(), traceId, authRequestURL, data, headers)
}
