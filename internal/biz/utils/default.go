package utils

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
)

// cachedStructInfo 保存了关于结构体类型的预解析信息，用于高效地填充默认值。
type cachedStructInfo struct {
	// 带有 'default' 标签的字段的设置器。
	setters []fieldSetter
	// nestedStructs 列出了作为结构体且应递归处理的字段索引。
	nestedStructs []int
	// nestedPtrs 列出了作为结构体指针且应递归处理的字段索引。
	nestedPtrs []int
}

// fieldSetter 保存单个字段的索引和预转换的默认值。
type fieldSetter struct {
	Index        int
	DefaultValue reflect.Value
}

// setterCache 存储每种结构体类型的 cachedStructInfo，以避免重复解析。
// 键是 reflect.Type，值是 *cachedStructInfo。
var setterCache sync.Map

// FillDefaults 使用缓存高效地从标签中填充结构体字段的默认值。
// 此函数针对频繁处理相同结构体类型的场景（如 HTTP 请求）进行了优化。
func FillDefaults(s any) error {
	value := reflect.ValueOf(s)
	if value.Kind() != reflect.Ptr || value.Elem().Kind() != reflect.Struct {
		return fmt.Errorf("input must be a pointer to a struct")
	}

	elem := value.Elem()
	t := elem.Type()

	// 1. 从缓存中获取结构体信息，如果缓存未命中则生成它。
	info, err := getOrGenerateStructInfo(t)
	if err != nil {
		return err
	}

	// 2. 对简单字段应用缓存的设置器。
	for _, setter := range info.setters {
		field := elem.Field(setter.Index)
		if field.IsZero() {
			field.Set(setter.DefaultValue)
		}
	}

	// 3. 递归处理嵌套的结构体。
	for _, idx := range info.nestedStructs {
		field := elem.Field(idx)
		if err := FillDefaults(field.Addr().Interface()); err != nil {
			return fmt.Errorf("failed to fill defaults for nested struct field %s: %w", t.Field(idx).Name, err)
		}
	}

	// 4. 递归处理嵌套的结构体指针。
	for _, idx := range info.nestedPtrs {
		field := elem.Field(idx)
		if field.IsNil() {
			// 使用新的结构体实例初始化指针。
			field.Set(reflect.New(field.Type().Elem()))
		}
		if err := FillDefaults(field.Interface()); err != nil {
			return fmt.Errorf("failed to fill defaults for nested pointer field %s: %w", t.Field(idx).Name, err)
		}
	}

	return nil
}

// getOrGenerateStructInfo 从缓存中检索结构体信息，如果不存在则生成它。
func getOrGenerateStructInfo(t reflect.Type) (*cachedStructInfo, error) {
	if cached, ok := setterCache.Load(t); ok {
		return cached.(*cachedStructInfo), nil
	}

	// 缓存中不存在，生成它。
	info := &cachedStructInfo{}
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fieldType := field.Type

		// 检查需要递归的嵌套结构体。
		// 注意：如果一个字段是结构体或结构体指针，它将不会因 'default' 标签而被处理。
		// 这是因为递归会处理嵌套结构体内部的字段。
		if fieldType.Kind() == reflect.Struct {
			info.nestedStructs = append(info.nestedStructs, i)
			continue
		}
		if fieldType.Kind() == reflect.Ptr && fieldType.Elem().Kind() == reflect.Struct {
			info.nestedPtrs = append(info.nestedPtrs, i)
			continue
		}

		defaultTag := field.Tag.Get("default")
		if defaultTag == "" {
			continue
		}

		// 将默认标签值转换为实际类型。
		convertedElem, err := convertString(defaultTag, fieldType)
		if err != nil {
			return nil, fmt.Errorf("field %s: %w", field.Name, err)
		}

		// 存储索引和转换后的 reflect.Value。
		info.setters = append(info.setters, fieldSetter{
			Index:        i,
			DefaultValue: reflect.ValueOf(convertedElem),
		})
	}

	// 将新生成的信息存储到缓存中。
	// 使用 LoadOrStore 来处理多个 goroutine 可能同时尝试生成的竞争条件。
	actual, _ := setterCache.LoadOrStore(t, info)
	return actual.(*cachedStructInfo), nil
}

// convertString 将字符串转换为指定类型
func convertString(value string, targetType reflect.Type) (interface{}, error) {
	switch targetType.Kind() {
	case reflect.Int:
		intValue, parseErr := strconv.ParseInt(value, 10, 0)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to int: %v", value, parseErr)
		}
		return int(intValue), nil
	case reflect.Int8:
		intValue, parseErr := strconv.ParseInt(value, 10, 8)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to int8: %v", value, parseErr)
		}
		return int8(intValue), nil
	case reflect.Int16:
		intValue, parseErr := strconv.ParseInt(value, 10, 16)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to int16: %v", value, parseErr)
		}
		return int16(intValue), nil
	case reflect.Int32:
		intValue, parseErr := strconv.ParseInt(value, 10, 32)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to int32: %v", value, parseErr)
		}
		return int32(intValue), nil
	case reflect.Int64:
		intValue, parseErr := strconv.ParseInt(value, 10, 64)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to int64: %v", value, parseErr)
		}
		return intValue, nil
	case reflect.Uint:
		uintValue, parseErr := strconv.ParseUint(value, 10, 0)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to uint: %v", value, parseErr)
		}
		return uint(uintValue), nil
	case reflect.Uint8:
		uintValue, parseErr := strconv.ParseUint(value, 10, 8)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to uint8: %v", value, parseErr)
		}
		return uint8(uintValue), nil
	case reflect.Uint16:
		uintValue, parseErr := strconv.ParseUint(value, 10, 16)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to uint16: %v", value, parseErr)
		}
		return uint16(uintValue), nil
	case reflect.Uint32:
		uintValue, parseErr := strconv.ParseUint(value, 10, 32)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to uint32: %v", value, parseErr)
		}
		return uint32(uintValue), nil
	case reflect.Uint64:
		uintValue, parseErr := strconv.ParseUint(value, 10, 64)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to uint64: %v", value, parseErr)
		}
		return uintValue, nil

	case reflect.Float32:
		floatValue, parseErr := strconv.ParseFloat(value, 32)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to float32: %v", value, parseErr)
		}
		return float32(floatValue), nil

	case reflect.Float64:
		floatValue, parseErr := strconv.ParseFloat(value, 64)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to float64: %v", value, parseErr)
		}
		return floatValue, nil

	case reflect.String:
		return value, nil

	case reflect.Bool:
		boolValue, parseErr := strconv.ParseBool(value)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to bool: %v", value, parseErr)
		}
		return boolValue, nil

	case reflect.Slice:
		// 将逗号分隔的字符串转换为切片
		if targetType.Elem().Kind() == reflect.String {
			return strings.Split(value, ","), nil
		}
		sliceValues := strings.Split(value, ",")
		slice := reflect.MakeSlice(targetType, len(sliceValues), len(sliceValues))
		for i, v := range sliceValues {
			elemValue, elemErr := convertString(strings.TrimSpace(v), targetType.Elem())
			if elemErr != nil {
				return nil, fmt.Errorf("failed to convert %q to slice element: %v", v, elemErr)
			}
			slice.Index(i).Set(reflect.ValueOf(elemValue))
		}
		return slice.Interface(), nil

	default:
		// 处理不支持的类型
		return nil, fmt.Errorf("unsupported target type: %v", targetType)
	}
}