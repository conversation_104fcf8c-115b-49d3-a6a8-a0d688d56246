package config

import (
	"fmt"
	"os"
	"path/filepath"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/model"
	"github.com/bytedance/sonic"
)

func LoadServiceConfig(path string) (*model.ServiceConfig, error) {
	if path == "" {
		return nil, fmt.Errorf(consts.ErrConfigPathEmpty)
	}

	// 检查文件是否存在
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil, fmt.Errorf("%s: %s", consts.ErrConfigFileNotFound, path)
	}

	// 检查文件扩展名
	if ext := filepath.Ext(path); ext != ".json" {
		return nil, fmt.Errorf("%s: %s (only .json supported)", consts.ErrUnsupportedFormat, ext)
	}

	configData, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	if len(configData) == 0 {
		return nil, fmt.Errorf("%s: %s", consts.ErrConfigFileEmpty, path)
	}

	config := model.ServiceConfig{
		Timeout:           consts.DefaultTimeout,
		DefaultPipeline:   consts.DefaultPipeline,
		HTTPClientTimeout: 10, // 默认10秒
	}

	if err := sonic.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config JSON: %w", err)
	}

	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("%s: %w", consts.ErrInvalidConfig, err)
	}

	return &config, nil
}

func validateConfig(config *model.ServiceConfig) error {
	if config.Host == "" {
		return fmt.Errorf("host is empty")
	}
	if config.AppID == "" {
		return fmt.Errorf("appID is empty")
	}
	if config.APISecret == "" {
		return fmt.Errorf("apiSecret is empty")
	}
	if config.APIKey == "" {
		return fmt.Errorf("apiKey is empty")
	}
	if config.Timeout <= 0 {
		return fmt.Errorf("timeout must be positive, got: %d", config.Timeout)
	}
	if config.DefaultPipeline == "" {
		return fmt.Errorf("defaultPipeline is empty")
	}
	if config.HTTPClientTimeout <= 0 {
		return fmt.Errorf("httpClientTimeout must be positive, got: %d", config.HTTPClientTimeout)
	}
	return nil
}
