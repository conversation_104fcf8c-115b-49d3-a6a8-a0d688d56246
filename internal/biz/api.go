package biz

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/service"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/utils"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

// Init 初始化函数
func Init() error {
	// 解析自定义toml配置块
	modelConfig := &model.ModelConfig{}

	err := goboot.UnmarshalConf(modelConfig)
	if err != nil {
		return err
	}

	c, err := config.LoadServiceConfig(modelConfig.Config.ConfigPath)
	if err != nil {
		return err
	}
	service.BizService.Config = c

	// 初始化HTTP客户端
	utils.InitHttpClient(c.HTTPClientTimeout)

	// 注册路由
	service.BizService.RegisterRouter()

	return nil
}
