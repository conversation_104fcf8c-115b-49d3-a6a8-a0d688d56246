package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/utils"
	proto_biz "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/biz"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/bytedance/sonic"
	"golang.org/x/sync/errgroup"
)

const (
	// defaultAuditQuestionDesc 默认的审核问题描述 (保持原逻辑中的静态值)
	defaultAuditQuestionDesc = `{"suggest":"pass","content":"","system":"","seq":0,"category_list":[],"request_id":"T202409191420011197d18ef88306000","sid":"e266c1e507d54d4b92f6b3084dea37dd","error_msg":"","audit_type":"","safe_classification":"13b_safe_model"}`
)

type bizService struct {
	Config *model.ServiceConfig
}

// BizService 业务服务单例，使用单例模式
var BizService = new(bizService)

func jsonUnmarshal(out []byte, req *pandora_proto.PandoraRequestMessage[model.Request]) error {
	if err := utils.FillDefaults(&req.Payload); err != nil {
		return err
	}
	return sonic.Unmarshal(out, req)
}

// RegisterRouter 注册路由
func (s *bizService) RegisterRouter() {
	router := goboot.HttpServer().DefaultInstance().Router
	handler := proto_biz.ProtoBizAPIV2.GinWrapper().SetHandler(s.BizProcess).SetRequestUnmarshal(jsonUnmarshal).HandlerFunc()

	// 保留biz-search/api/v2，兼容老版本
	// 后续希望保留biz/api/v2
	paths := []string{"/biz-search/api/v2", "/biz/api/v2"}
	for _, path := range paths {
		router.POST(path, handler)
	}
}

// BizProcess 业务处理
func (s *bizService) BizProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request]) *pandora_proto.PandoraResponseMessage[model.Response] {
	span := ctx.RootSpan().AddSpan("pandora_process")
	defer span.Finish()

	var selfErr *errtypes.SelfError
	resp := proto_biz.ProtoBizAPIV2.NewPandoraResponseMessage()
	resp.Header.TraceId = req.Header.TraceId
	resp.Header.AppId = req.Header.AppId

	defer func() {
		if selfErr != nil {
			span.TraceInfo("selfError", selfErr)
			resp.Header.Code = selfErr.Code()
			resp.Header.Success = selfErr.String()
		}
	}()

	// 参数校验
	if selfErr = s.ValidateParameters(req); selfErr != nil {
		return resp
	}

	// 执行处理
	resp.Payload.Results, selfErr = s.executeWithTimeout(ctx, req)

	return resp
}

// 执行带有超时的函数
func (s *bizService) executeWithTimeout(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request]) ([]*model.BizSearchData, *errtypes.SelfError) {
	// 创建一个带有超时的上下文
	ctxTimeout, cancel := context.WithTimeout(ctx.GetContext(), time.Duration(s.Config.Timeout)*time.Millisecond)
	defer cancel()
	var results []*model.BizSearchData

	// 遍历请求中的查询，将查询结果添加到结果列表中
	// 外部预制结果是为了后续超时能返回已经有的结果
	// 预制超时，内部处理错误码覆盖，成功也覆盖success
	for _, query := range req.Payload.Query {
		results = append(results, &model.BizSearchData{
			Query: query,
			Code:  selferrors.BizSearchError_Timeout.Code(),
			Msg:   selferrors.BizSearchError_Timeout.String(),
		})
	}

	done := make(chan struct{})
	go func() {
		defer close(done)
		s.Processor(ctx, req, results)
	}()

	// 选择返回结果或超时
	select {
	case <-done:
		return results, nil
	case <-ctxTimeout.Done():
		return results, nil // 返回可能已部分处理的结果
	}
}

// 定义一个名为Processor的方法，该方法属于Service结构体
func (s *bizService) Processor(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request], results []*model.BizSearchData) {
	span := ctx.RootSpan().AddSpan("processor_biz")
	defer span.Finish()

	g, gCtx := errgroup.WithContext(ctx.GetContext())
	g.SetLimit(consts.MaxConcurrentRequests)

	for i := range results {
		// 在 Go 1.22+ 中，循环变量在每次迭代中都是新实例，因此这里直接使用是安全的。
		// 对于旧版本，需要创建副本: i := i
		g.Go(func() error {
			select {
			case <-gCtx.Done():
				return gCtx.Err()
			default:
			}
			spanChild := span.AddSpan("processor_biz_child")
			defer spanChild.Finish()
			result := results[i]

			bizRequest := &model.BizRequest{
				AuditQuestionDesc: defaultAuditQuestionDesc,
				PipelineName:      req.Payload.Pipeline,
				Sid:               req.Header.TraceId,
				UID:               "",
				AppID:             s.Config.AppID,
				Limit:             strconv.Itoa(req.Payload.Limit),
				Business:          req.Payload.Business,
				Name:              result.Query,
				DisableHighlight:  !req.Payload.Highlight,
				OpenRerank:        req.Payload.OpenRerank,
				FullText:          req.Payload.FullText,
				DisableCrawler:    !req.Payload.Crawler,
				Timestamp:         time.Now().Unix(),
			}

			var serr *errtypes.SelfError
			defer func() {
				if serr != nil {
					result.Code = serr.Code()
					result.Msg = serr.String()
				}
			}()

			// 生成请求数据
			spanChild.TraceInfo("request", bizRequest)
			bizRequestData, err := sonic.Marshal(bizRequest)
			if err != nil {
				serr = selferrors.BizSearchError_JsonMarshal.Detaild(err.Error())
				return serr
			}
			authRequestURL, bizData, headers, err := utils.Prepare(s.Config.Host, bizRequestData, "POST", s.Config.AppID, s.Config.APIKey, s.Config.APISecret)
			if err != nil {
				serr = selferrors.BizSearchError_RequestFailed.Detaild("failed to prepare request: " + err.Error())
				return serr
			}

			spanChild.TraceInfo("authRequestURL", authRequestURL)
			spanChild.TraceInfo("data", bizData)
			spanChild.TraceInfo("headers", headers)

			// 发送请求
			bizResponseData, err := utils.ExecuteWithContext(gCtx, req.Header.TraceId, authRequestURL, bizData, headers)
			if err != nil {
				serr = selferrors.BizSearchError_CalcFailed.Detaild(err.Error())
				return serr
			}
			spanChild.TraceInfo("response", bizResponseData)

			// 解析响应数据
			var bizResponse model.BizResponse
			err = sonic.Unmarshal(bizResponseData, &bizResponse)
			if err != nil {
				serr = selferrors.BizSearchError_ResponseDecode.Detaild(err.Error())
				return serr
			}

			// 验证响应数据
			if bizResponse.ErrCode != "0" {
				serr = selferrors.BizSearchError_CodeFailed.Detaild(fmt.Sprintf("response code is not 0 , is %v", bizResponse.ErrCode))
				return serr
			}

			// 将响应数据转换为结果数据
			resultItems := []*model.BizSearchItem{}
			for _, doc := range bizResponse.Data.Documents {
				content := doc.Content
				if content == "" {
					content = doc.Summary
				}
				title := doc.Name
				if title == "" {
					title = truncateStringByRunes(doc.Summary, 32)
				}
				resultItems = append(resultItems, &model.BizSearchItem{
					Title:           title,
					Summary:         doc.Summary,
					Content:         content,
					Url:             doc.URL,
					Score:           doc.FinalScore,
					Img:             doc.Img,
					FrScore:         doc.FrScore,
					TimeWeightScore: doc.TimeWeightScore,
					Source:          doc.Source,
					TimeScore:       doc.TimeScore,
					SiteName:        doc.SiteName,
					SiteWeightScore: doc.SiteWeightScore,
					URLRepeatScore:  doc.URLRepeatScore,
					DailyDiff:       doc.DailyDiff,
					PublishedDate:   doc.PublishedDate,
					ExtractDate:     doc.ExtractDate,
				})
			}
			result.Data = resultItems
			serr = selferrors.SuccessTypes_Success
			return nil
		})
	}

	// 等待所有goroutine完成
	g.Wait()

}

// ValidateParameters 验证请求参数
func (s *bizService) ValidateParameters(req *pandora_proto.PandoraRequestMessage[model.Request]) *errtypes.SelfError {

	// 如果请求参数中的查询为空，则返回错误
	if len(req.Payload.Query) == 0 {
		return selferrors.CommonError_InvalidInput.Detaild("queries is empty")
	}

	// 如果请求参数中的appid为空，则返回错误
	if req.Header.AppId == "" {
		return selferrors.CommonError_InvalidInput.Detaild("appid is empty")
	}

	// 返回nil表示验证通过
	return nil
}

// truncateStringByRunes 按rune截断字符串，避免UTF-8字符被截断一半
func truncateStringByRunes(s string, length int) string {
	runes := []rune(s)
	if len(runes) > length {
		return string(runes[:length])
	}
	return s
}
