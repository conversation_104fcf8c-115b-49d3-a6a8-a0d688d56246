package config

import (
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
	wrapper "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

const TOML_MODEL = "rerank_models"

var ModelFactory = base_factory.NewFactory(TOML_MODEL, NewModelsInstance)
var G_ModelModule = goboot.GetCustomModule(ModelFactory)

// ModelConfig 模型配置
type ModelConfig struct {
	base_model.BaseModel
	Timeout   int `toml:"timeout"`
	BatchSize int `toml:"batch_size"`
}

type AseItem struct {
	AseConf   *ModelConfig
	AseSdk    *aseclient.InferClient
	Tokenizer *wrapper.AsyncTokenizer
}

func NewModelsInstance(option *ModelConfig) (*ModelConfig, error) {

	return option, nil
}
