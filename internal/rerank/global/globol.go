package global

import (
	"fmt"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/async/antspool"
	tokenizerv2_1 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

var (
	IFLYS_SAMPLE_Tokenizer *tokenizerv2_1.TokenizerWrapper
	IFLYS_TokenModelServer *tokenizerv2_1.AsyncTokenizer
	Inst                   *antspool.AsyncAntsPool
	AseMap                 map[string]*config.AseItem
)

var TermWeightVersion string = "termweight_v20231221"

func Init() error {
	// 初始化资源和公共实例
	IFLYS_TokenModelServer = tokenizerv2_1.G_tokenizer.DefaultInstance()
	IFLYS_SAMPLE_Tokenizer = tokenizerv2_1.NewSimpleTokenizer(tokenizerv2_1.G_tokenizer.DefaultConfig().Path, tokenizerv2_1.G_tokenizer.DefaultConfig().UseLocal)

	Inst = goboot.AntsPool().DefaultInstance()

	models := goboot.GetCustomModule(config.ModelFactory).GetAllConfig()
	fmt.Println("models", models)
	AseMap = make(map[string]*config.AseItem, len(models))
	for _, model := range models {
		item := &config.AseItem{}
		item.AseConf = model

		if item.Tokenizer = tokenizerv2_1.G_tokenizer.GetInstance(model.UName); item.Tokenizer == nil {
			return selferrors.RerankError_ModelNotSupport.Detaild(fmt.Sprintf("invalid tokenizer: %s", model.UName))
		}

		if item.AseSdk = goboot.AseSdk().GetInstance(model.UName); item.AseSdk == nil {
			return selferrors.RerankError_ModelNotSupport.Detaild(fmt.Sprintf("invalid ase sdk: %s", model.UName))
		}

		AseMap[model.UName] = item
	}
	fmt.Println("AseMap", AseMap)
	return nil
}
