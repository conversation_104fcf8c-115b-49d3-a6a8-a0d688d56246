package rank

import (
	"fmt"
	"runtime"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"

	"golang.org/x/sync/errgroup"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"

	"sort"
)

// Segcalc 分词计算
func (s *MedicalRerank) Segcalc(req *bean.Request, span *span.Span, query string) (bean.SegRes, error) {

	feature, err := global.Inst.Submit(func() (any, error) {
		var m bean.SegRes
		var err error
		if req.Payload.Model == consts.Medical_V0620 {
			m, err = utils.TitleSeg0620(req, span, strings.TrimSpace(query))
		} else {
			m, err = utils.TitleSeg(req, span, strings.TrimSpace(query))
		}

		return m, err
	})

	if err != nil {
		return bean.SegRes{}, err
	}

	r, err := feature.Result()
	if err != nil {
		return bean.SegRes{}, fmt.Errorf("Segcalc error: %w, query:%s", err, query)
	}

	out := r.(bean.SegRes)
	return out, nil
}

// processQuery 处理查询词
func (s *MedicalRerank) processQuery(req *bean.Request, span *span.Span, query string) ([]string, []float64, error) {
	opSpan := span.AddSpan("query分词处理")
	defer opSpan.Finish()
	var out bean.SegRes
	var err error
	if req.Payload.Model == consts.Medical_V0620 {
		out, err = utils.QuerySeg0620(req, span, strings.TrimSpace(query))
	} else {
		out, err = utils.QuerySeg(req, span, strings.TrimSpace(query))
	}
	if err != nil {
		return nil, nil, err
	}
	return out.Keys, out.Value, nil
}

// processTitle 处理每个文档的标题
func (s *MedicalRerank) processTitle(g *errgroup.Group, req *bean.Request, query string, span *span.Span, data *bean.QueryData, titleKeywordsKeysList [][]string, titleKeywordsValuesList [][]float64) ([]string, []float64, error) {
	defer func() {
		span.Finish()
	}()
	var queryKeywordsKeys []string
	var queryKeywordsValues []float64

	g.Go(func() error {
		var err error
		queryKeywordsKeys, queryKeywordsValues, err = s.processQuery(req, span, query)
		return err
	})
	titleSegSpan := span.AddSpan("title分词处理")
	defer titleSegSpan.Finish()
	for index := range data.Docs {
		index := index // 避免闭包问题
		// 检查是否需要处理当前文档
		if req.Payload.Model == consts.Medical_V0620 && index >= 16 {
			continue
		}
		g.Go(func() error {
			title := common.Interface2S((*data.Docs[index])["title"])
			if req.Payload.Model == consts.Medical_V0620 {
				title = strings.ToUpper(title)
			}
			out, err := s.Segcalc(req, span, title)
			if err != nil {
				return err
			}

			// 填充结果
			titleKeywordsKeysList[index] = out.Keys
			titleKeywordsValuesList[index] = out.Value
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return queryKeywordsKeys, queryKeywordsValues, err
	}
	return queryKeywordsKeys, queryKeywordsValues, nil
}

// calculateWordScore 计算 wordScore
func (s *MedicalRerank) calculateWordScore(g *errgroup.Group, req *bean.Request, span *span.Span, query string, data *bean.QueryData, rerankList []*map[string]any, queryKeywordsKeys []string, queryKeywordsValues []float64, titleKeywordsKeysList [][]string, titleKeywordsValuesList [][]float64) error {
	concurrencyLimit := runtime.NumCPU()
	docCount := len(data.Docs)
	if docCount < concurrencyLimit {
		concurrencyLimit = docCount
	}
	g.SetLimit(concurrencyLimit)

	for index, doc := range data.Docs {
		index := index // 避免闭包问题
		rerankList[index] = common.DeepCopyMap(doc)
		if req.Payload.Model == consts.Medical_V0620 {
			(*rerankList[index])["tag"] = true
		}

		if req.Payload.Model == consts.Medical_V0620 {

			if index <= 16 {
				g.Go(func() error {
					title := common.Interface2S((*doc)["title"])
					content := common.Interface2S((*doc)["content"])

					keywordsScore, matchDict, unmatchDict, unmatchDict_, err := utils.MatchWordsScore0620(query, title, content, queryKeywordsKeys, queryKeywordsValues, titleKeywordsKeysList[index], titleKeywordsValuesList[index])
					if err != nil {
						return err
					}

					(*rerankList[index])["words_score"] = keywordsScore
					(*rerankList[index])["keywords_score"] = keywordsScore
					(*rerankList[index])["matchDict"] = matchDict
					(*rerankList[index])["unmatchDict"] = unmatchDict
					(*rerankList[index])["unmatchDict_"] = unmatchDict_
					return nil
				})
			} else {
				(*rerankList[index])["words_score"] = 0
				(*rerankList[index])["keywords_score"] = 0
			}
		} else {
			g.Go(func() error {
				title := common.Interface2S((*doc)["title"])
				keywordsScore, err := utils.MatchWordsScore0310(query, title, queryKeywordsKeys, queryKeywordsValues, titleKeywordsKeysList[index], titleKeywordsValuesList[index])
				if err != nil {
					return err
				}
				(*rerankList[index])["words_score"] = keywordsScore
				(*rerankList[index])["keywords_score"] = keywordsScore
				return nil
			})
		}
	}
	return g.Wait()
}

// QueryKeywords 封装查询关键词的键值对
type QueryKeywords struct {
	Keys   []string
	Values []float64
}

// DocProcess 文档处理，计算词匹配得分
func (s *MedicalRerank) DocProcess(req *bean.Request, span *span.Span, data *bean.QueryData, rerankList []*map[string]any) error {
	opSpan := span.AddSpan("计算词匹配得分")
	defer opSpan.Finish()

	// 限制 query 最大长度为20个字符
	query := data.Query
	if len([]rune(query)) > 20 && req.Payload.Model == consts.Medical_V0620 {
		query = strings.ToUpper(string([]rune(query)[:20]))
	}

	// 提前分配内存
	docCount := len(data.Docs)
	titleKeywordsKeysList := make([][]string, docCount, docCount)
	titleKeywordsValuesList := make([][]float64, docCount, docCount)

	var g errgroup.Group
	// 使用 QueryKeywords 结构体指针
	var queryKeywordsKeys []string
	var queryKeywordsValues []float64

	// 处理每个文档的标题
	var err error
	if queryKeywordsKeys, queryKeywordsValues, err = s.processTitle(&g, req, query, opSpan, data, titleKeywordsKeysList, titleKeywordsValuesList); err != nil {
		return err
	}

	if err := g.Wait(); err != nil {
		return err
	}
	// 计算 wordScore
	if err := s.calculateWordScore(&g, req, span, query, data, rerankList, queryKeywordsKeys, queryKeywordsValues, titleKeywordsKeysList, titleKeywordsValuesList); err != nil {
		return err
	}

	return nil
}

// Rank0310 重排排序
func (s *MedicalRerank) Rank0310(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, []*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	rankSpan.Finish()

	// 中间过程明细，用于溯源
	// id2ScoreDetail := make(map[int64]*map[string]any)
	scoreDetails := make([]map[string]any, len(data.Docs), len(data.Docs))
	defer func() {
		sort.Slice(scoreDetails, func(i, j int) bool {
			score1 := common.Interface2F64(scoreDetails[i]["_rerank_score"])
			score2 := common.Interface2F64(scoreDetails[j]["_rerank_score"])
			if score1 == score2 {
				rank_index1 := common.Interface2F64(scoreDetails[i]["_rank_score"])
				rank_index2 := common.Interface2F64(scoreDetails[j]["_rank_score"])
				return rank_index1 < rank_index2
			}
			return score1 > score2
		})
		rankSpan.TraceInfo("scoreDetail", scoreDetails)

	}()

	// rerankScore为最终结果
	// TODO: 预先分配长度
	rerankScore := make([]*map[string]any, 0)
	// rerankList为中间结果
	rerankList := make([]*map[string]any, len(data.Docs))

	// doc处理计算词匹配得分
	err := s.DocProcess(req, rankSpan, data, rerankList)
	if err != nil {
		return nil, nil, err
	}

	// 词匹配得分排序
	sort.Slice(rerankList, func(i, j int) bool {
		wordsScoreI := common.Interface2F64((*rerankList[i])["words_score"])
		wordsScoreJ := common.Interface2F64((*rerankList[j])["words_score"])
		if wordsScoreI == wordsScoreJ {
			return common.Interface2F64((*rerankList[i])["_rank_score"]) > common.Interface2F64((*rerankList[j])["_rank_score"])
		}
		return wordsScoreI > wordsScoreJ
	})

	// 不易读，可以合并
	topFive := utils.PostProcess1(rankSpan, req.Payload.Model, rerankList)

	utils.PostProcess2(rankSpan, req.Payload.Model, rerankList, topFive)

	if req.Payload.Model == consts.Medical_V0310 {
		utils.PostProcess3(rankSpan, rerankList, topFive)
	}

	// 最终得分计算
	scoreMap := make(map[int64]float64)
	for index, doc := range rerankList {
		id := common.Interface2I64((*doc)["id"])
		url := utils.ParseUrl(doc)
		indexThreshold := 9999
		if req.Payload.Model == consts.Medical_V0620 {
			indexThreshold = 15
		}
		score := 0.0
		if req.Payload.Model == consts.Medical_V0310 {
			score = common.Interface2F64((*doc)["keywords_score"])
		} else {
			score = common.Interface2F64((*doc)["words_score"])
		}
		urlScore := UrlsScore(
			req,
			url,
			common.Interface2S((*doc)["domain"]),
			score,
			int(common.Interface2I64((*doc)["_rank_index"])),
			common.Interface2Bool((*doc)["tag"]),
			config.MedicalDomais,
			indexThreshold,
		)
		var docLevel int
		// 质量等级获取和得分计算
		if req.Payload.Model == consts.Medical_V0310 {
			docLevel = int(common.Interface2I64((*doc)["q_user"]))
			if docLevel == 0 {
				docLevel = int(common.Interface2I64((*doc)["q_level"]))
			}
		} else {
			if t, ok := (*doc)["levels"].(map[string]any); ok {
				// fmt.Println(t)
				for key, value := range t {
					if common.Interface2S(key) == config.G_ParamModule.DefaultInstance().HealthLevel {
						docLevel = int(common.Interface2I64(value))
					}

				}
			} else {
				docLevel = 1
			}
		}

		var levelScore float64
		if req.Payload.Model == consts.Medical_V0620 {
			levelScore = levelsScore0620(docLevel)
		} else {
			levelScore = levelsScore(docLevel)
		}

		rerankScore := common.Interface2F64((*doc)["words_score"]) + (common.Interface2F64((*doc)["_rank_score"])-0.8)*10 + urlScore + levelScore
		(*doc)["_rerank_score"] = rerankScore
		scoreMap[id] = rerankScore

		scoreDetail := map[string]any{
			"id":             id,
			"url_score":      urlScore,
			"url":            url,
			"level_score":    levelScore,
			"words_score":    common.Interface2F64((*doc)["words_score"]),
			"keywords_score": common.Interface2F64((*doc)["keywords_score"]),
			"_rank_score":    (common.Interface2F64((*doc)["_rank_score"]) - 0.8) * 10,
			"_rerank_score":  rerankScore,
			"unmatchDict":    (*doc)["unmatchDict"],
			"matchDict":      (*doc)["matchDict"],
			"unmatchDict_":   (*doc)["unmatchDict_"],
			"tag":            common.Interface2Bool((*doc)["tag"]),
		}
		// id2ScoreDetail[id] = scoreDetail
		scoreDetails[index] = scoreDetail
	}

	// // 最终排序
	sort.Slice(rerankList, func(i, j int) bool {
		rerankScoreI := common.Interface2F64((*rerankList[i])["_rerank_score"])
		rerankScoreJ := common.Interface2F64((*rerankList[j])["_rerank_score"])
		if rerankScoreI == rerankScoreJ {
			return common.Interface2F64((*rerankList[i])["_rank_score"]) > common.Interface2F64((*rerankList[j])["_rank_score"])
		}
		return rerankScoreI > rerankScoreJ
	})
	// 筛选得分大于等于阈值的文档
	for _, doc := range data.Docs {
		score := common.Interface2F64((*doc)["_rank_score"])
		if score >= req.Payload.ScoreThreshold {
			id := common.Interface2I64((*doc)["id"])
			(*doc)["_rerank_score"] = scoreMap[id]
			rerankScore = append(rerankScore, doc)
		}
	}

	return utils.FinalResultsProcess(rankSpan, req, rerankScore, true)
}
