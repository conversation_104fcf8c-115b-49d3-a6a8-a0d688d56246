package rank

import (
	"fmt"

	"math"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	"github.com/dexyk/stringosim"
)

type MedicalRerank struct {
	Ctx *pandora_context.PandoraContext
}

var MedicalRerankInst = &MedicalRerank{}

func (r *MedicalRerank) score_origin(score float64) float64 {
	if score == 1 {
		return 16
	}
	originScore := -math.Log((1 / score) - 1)
	return originScore
}

func (r *MedicalRerank) answerStage2(data []*map[string]any, rank_threshold float64, span *span.Span) [][]*map[string]any {
	opSpan := span.AddSpan("answerStage")
	defer opSpan.Finish()
	records := make([]*map[string]any, 0)
	scoreList := make([]float64, 0)
	for _, d := range data {
		rankScore := common.Interface2F64((*d)["_rank_score"])
		if rankScore > 0.5 {
			records = append(records, d)
			rankScore = r.score_origin(rankScore)
			scoreList = append(scoreList, rankScore)
		}
	}
	// 创建一个与 scoreList 长度相同的索引切片
	indices := make([]int, len(scoreList))
	for i := range indices {
		indices[i] = i
	}

	// 将索引和分数配对并生成字典
	scoreDict := make(map[int]float64)
	for i, score := range scoreList {
		scoreDict[indices[i]] = score
	}

	// 根据分数对字典进行排序（从高到低）
	type kv struct {
		Key   int
		Value float64
	}
	var sortedScoreDict []kv
	for k, v := range scoreDict {
		sortedScoreDict = append(sortedScoreDict, kv{k, v})
	}
	sort.Slice(sortedScoreDict, func(i, j int) bool {
		return sortedScoreDict[i].Value > sortedScoreDict[j].Value
	})

	// 初始化一个包含17个空列表的列表
	truncatThresholds := make([]int, 17)
	for i := range truncatThresholds {
		truncatThresholds[i] = i
	}
	clusterList := make([][]*map[string]any, len(truncatThresholds))
	for i := range clusterList {
		clusterList[i] = []*map[string]any{}
	}

	// 根据分数将记录分配到相应的列表中
	for _, kv := range sortedScoreDict {
		index := kv.Key
		score := kv.Value
		clusterList[int(score)] = append(clusterList[int(score)], records[index])
	}

	// 过滤掉空列表，并将结果反转
	var recordsBox [][]*map[string]any
	for i := len(clusterList) - 1; i >= 0; i-- {
		if len(clusterList[i]) > 0 {
			recordsBox = append(recordsBox, clusterList[i])
		}
	}
	return recordsBox

}

func (r *MedicalRerank) answerStage(data []*map[string]any, step float64, rankScoreThreshold float64) [][]*map[string]any {
	// 得分分桶

	scoreHist := arange(rankScoreThreshold, 1.05, step)
	shLen := len(scoreHist)

	recordsBox := make([][]*map[string]any, 0)
	if step == 0 {
		var recordBox []*map[string]any
		if step == 0 || step == 1 {
			for i := 0; i < len(data); i++ {
				(*data[i])["_rerank_score"] = rankScoreThreshold
				recordBox = append(recordBox, data[i])
			}
		}
		if len(recordBox) > 0 {
			recordsBox = append(recordsBox, recordBox)
		}
		return recordsBox
	}
	for idx := range scoreHist {
		var ec float64
		sc := scoreHist[idx]
		if idx >= shLen-1 {
			ec = 1 + step
		} else {
			ec = scoreHist[idx+1]
		}

		var recordBox []*map[string]any
		for idx1 := 0; idx1 < len(data); idx1++ {
			score := common.Interface2F64((*data[idx1])["_rank_score"])
			// score, ok := (*data[idx1])["rank_score"].(float64)
			// if !ok {
			// 	// score, ok = (*data[idx1])["rank_score"].(int32)
			// 	// if !ok {
			// 	// 	score = 0
			// 	// }
			// 	score = 0
			// }
			if score >= sc && score < ec {
				(*data[idx1])["_rerank_score"] = sc
				recordBox = append(recordBox, data[idx1])
			}
		}

		if len(recordBox) > 0 {
			recordsBox = append(recordsBox, recordBox)
		}
	}

	return recordsBox
}

func arange(start, stop, step float64) []float64 {
	var s []float64
	if step == 0 {
		s = append(s, start)

	} else {
		for i := start; i < stop; i += step {
			s = append(s, i)
		}
	}
	return s
}

func calcRerankScore(id int64, query string, timeDiff int64, summaryLen int, score, lcs, queryScore, summaryScore, keywordProp, keywordsLen, summaryKeywords,
	qualityScore, jaccardDist, levenshteinDist, suppressWeight float64, authority, pr, freq, top, strongTimeliness, domainFreq, realmFreq int32) (float64, map[string]any) {
	summaryScore = math.Tanh(float64(summaryLen)/10000) * 0.3
	lcsScore := float64(lcs) * 0.01 * suppressWeight
	jaccardScore := jaccardDist * 0.01 * suppressWeight
	keywordPropScore := keywordProp * 0.005
	keywordsLenScore := keywordsLen * 0.005
	keywordsSummaryScore := summaryKeywords * 0.01
	authorityScore := math.Tanh(float64(authority)) * 0.01
	levenshteinDistScore := common.Min(levenshteinDist, float64(utf8.RuneCountInString(query))) * -0.003 / suppressWeight
	domainFreqScore := math.Tanh(0.00000001*float64(domainFreq)) * 0.1

	var rankScore float64 = 0
	var rerankScore float64 = 0
	scoreDetail := make(map[string]any, 0)
	scoreDetail["summary_score"] = summaryScore // 0.014538602311465032
	rankScore += summaryScore
	scoreDetail["lcs_score"] = lcsScore // 0.01
	rankScore += lcsScore

	scoreDetail["jaccard_score"] = jaccardScore // 0.002
	rankScore += jaccardScore

	scoreDetail["keyword_prop_score"] = keywordPropScore //0.005
	rankScore += keywordPropScore

	scoreDetail["keywords_length_score"] = keywordsLenScore //0.005
	rankScore += keywordsLenScore

	scoreDetail["keywords_in_summary_score"] = keywordsSummaryScore //0.01
	rankScore += keywordsSummaryScore

	scoreDetail["authority_score"] = authorityScore //0
	rankScore += authorityScore

	scoreDetail["levenshtein_dist_score"] = levenshteinDistScore //-0.009000000000000001
	rankScore += levenshteinDistScore

	scoreDetail["domain_freq_score"] = domainFreqScore //0
	rankScore += domainFreqScore

	rerankScore = rankScore*0.13 + score*18
	// fmt.Printf("id:%v, 分数1:%v, 分数2:%v, 分数3:%v\n", id, rankScore, score, rerankScore)

	scoreDetail["levenshtein_dist"] = levenshteinDist
	scoreDetail["summary_length"] = summaryLen
	scoreDetail["lcs"] = lcs
	scoreDetail["jaccard_dist"] = jaccardDist
	scoreDetail["keyword_prop"] = keywordProp
	scoreDetail["keyword_length"] = keywordsLen

	return rerankScore, scoreDetail
}

func (r *MedicalRerank) rerankProcess(req *bean.Request, data *map[string]any, domainId string, query, category string, currentTime int64, score float64, mkb_tag bool) (float64, map[string]any) {
	// 如果有summary则用summary,无summary则使用content512
	// 如果有summary则用summary,无summary则使用content512
	var Summary = common.Interface2S((*data)["summary"])
	var Content = common.Interface2S((*data)["content"])
	if Summary == "" {
		Summary = common.TruncateString(Content, 512)
	}

	summary := common.If(Summary == "", "", strings.ToLower(strings.TrimSpace(Summary)))
	ts := common.Interface2I64((*data)["post_ts"])

	timeDiff := currentTime - ts
	title := common.Interface2S((*data)["title"])
	title = strings.ToLower(title)

	summaryLen := utf8.RuneCountInString(summary)

	// 使用下划线分割标题字符串
	titleSegs := strings.Split(title, "_")

	// 根据下划线的数量进行不同的处理
	if len(titleSegs) >= 3 {
		// 保留前部分的段，去掉最后两个段
		title = strings.Join(titleSegs[:len(titleSegs)-2], "_")
	} else if len(titleSegs) == 2 {
		// 只保留第一个段
		title = titleSegs[0]
	}

	queryKeywords := common.SetDifference(config.Seg.CutStop(query), config.StopWords)
	titleKeywords := common.SetDifference(config.Seg.CutStop(title), config.StopWords)
	rerankKeywords := make([]string, 0)

	for _, item := range titleKeywords {
		rerankKeywords = append(rerankKeywords, item)
	}
	// fmt.Println(rerankKeywords)
	(*data)["_rerank_keywords"] = strings.Join(rerankKeywords, " ")
	strongTimeliness := 1

	var wg sync.WaitGroup
	var levenshteinDist int
	var lcs, jaccardDist float64

	wg.Add(3)
	go func() {
		defer wg.Done()

		lcs = float64(stringosim.LCS([]rune(query), []rune(title))) / float64(utf8.RuneCountInString(query))
		// data.Lcs = lcs
	}()

	go func() {
		defer wg.Done()

		//jaccardDist = stringosim.Jaccard([]rune(query), []rune(title), []int{1})
		jaccardDist = common.JaccardDistance2(query, title)
		// slog.DebugF("id:%v,query:%s,title:%s,jaccard:%v\n", common.Interface2I64((*data)["id"]), query, title, jaccardDist)
	}()

	go func() {
		defer wg.Done()

		levenshteinDist = stringosim.Levenshtein([]rune(query), []rune(title))
	}()

	wg.Wait()

	// authority := data.Authority
	// top := data.Top

	// domain_freq := 0 //原从mongo读取？ 暂赋值0
	// domainFreq := int(data.DomainFreq)
	// if domainFreq <= 0 {
	// 	domainFreq = 0
	// 	data.DomainFreq = int32(domainFreq)
	// }
	realmFreq := 0
	freq := 1

	// pr := data.Pr

	queryScore, summaryScore, keywordProp, keywordsLen := keywordFeat2(queryKeywords, titleKeywords, title)

	summaryKeywords := 0
	for _, item := range queryKeywords {
		if strings.Contains(summary, item) {
			summaryKeywords++
		}
	}
	if len(queryKeywords) == 0 {
		summaryKeywords = 0
	} else {
		summaryKeywords = summaryKeywords / len(queryKeywords)
	}

	suppressWeight := 1.0
	if float64(len(title)) < float64(len(strings.Join(queryKeywords, "")))*0.4 {
		suppressWeight = 0.6
	}
	qualityScore := 0

	var domainFreq int32 = 0
	var authority int32 = 0
	var pr int32 = 0
	var top int32 = 0

	rerankScore, scoreDetail := calcRerankScore(common.Interface2I64((*data)["id"]), query, timeDiff, summaryLen, float64(score), lcs, float64(queryScore), float64(summaryScore), float64(keywordProp), float64(keywordsLen), float64(summaryKeywords),
		float64(qualityScore), jaccardDist, float64(levenshteinDist), suppressWeight, authority, pr, int32(freq), top, int32(strongTimeliness), int32(domainFreq), int32(realmFreq))

	// 去除视频类新闻
	minusVideo := true

	for key := range consts.VideoKeywords {
		if strings.Contains(query, key) {
			minusVideo = false
			break
		}
	}

	if minusVideo {
		for key := range consts.VideoKeywords {
			if strings.Contains(summary, key) {
				rerankScore -= 2
			}
		}
	}
	scoreDetail["minusVideo"] = minusVideo

	// 领域分类
	// var realm string = ""
	// if len(category) > 0 && category == realm {
	// 	rerankScore = rerankScore * 1.5
	// }

	scoreDetail["score_without_level_score"] = rerankScore

	var docLevel int = 1

	// docLevel = int(common.Interface2I64((*data)["q_level"]))
	if t, ok := (*data)["levels"].(map[string]any); ok {
		// fmt.Println(t)
		for key, value := range t {
			if common.Interface2S(key) == config.G_ParamModule.DefaultInstance().HealthLevel {
				docLevel = int(common.Interface2I64(value))
			}

		}
	} else {
		docLevel = 1
	}
	// slog.DebugF("质量得分:%v", docLevel)
	var AuthorScore int32 = 0
	levelScore := CalMedicalScore(float64(AuthorScore), int(docLevel))
	scoreDetail["level_socre"] = levelScore
	var url string
	protocal, ok := (*data)["protocol"].(string)
	if !ok {
		protocal = ""
	}
	domain, ok := (*data)["domain"].(string)
	if !ok {
		domain = ""
	}
	path, ok := (*data)["path"].(string)
	if !ok {
		path = ""
	}
	url = common.Interface2S((*data)["url"])
	if len(url) == 0 {
		if strings.Contains(protocal, "://") {
			url = fmt.Sprintf("%s%s%s", protocal, domain, path)
		} else {
			url = fmt.Sprintf("%s://%s%s", protocal, domain, path)
		}
	}
	// 判断是不是讯飞医典的数据
	if domain == "mkb.iflyhealth.com" && !mkb_tag {
		scoreDetail["mkb_tag_score"] = 0.5
		rerankScore += 0.5
		mkb_tag = true
	}
	scoreDetail["mkb_tag_score"] = 0

	urlTag := false
	indexCode, ok := (*data)["_indexCode"].(string)
	if !ok {
		indexCode = ""
	}
	// site, ok := (*data)["site"].(string)
	// if !ok {
	// 	site = ""
	// }
	// if strings.Contains(url, "m.baidu.com/bh/m/detail/ar") || strings.Contains(url, "health.baidu.com/m/detail/ar") {
	// 	urlTag = true
	// }
	if len(req.Payload.RankSites) != 0 {
		for _, site := range req.Payload.RankSites {
			if strings.Contains(url, site) && !urlTag {
				urlTag = true
			}
		}
	}
	if len(req.Payload.RankCollections) == 0 {
		if value, ok := config.RankCollectionsMap[domainId]; ok {
			if indexCode != "" && common.Contains(value, indexCode) && !urlTag {
				urlTag = true
			}
		}

	} else {
		if indexCode != "" && common.Contains(req.Payload.RankCollections, indexCode) {
			urlTag = true
		}
	}

	isMedical := false
	//  判断是否为医疗领域站点
	if strings.Contains(url, "baidu.com/m/detail") || strings.Contains(url, "baidu.com/bh/m/detail") {
		isMedical = true
	} else {
		domain := strings.Split(strings.Split(url, "//")[1], "/")[0]
		for _, medicalDomain := range config.MedicalDomais {
			if strings.Contains(domain, medicalDomain) {
				isMedical = true
			}
		}
	}

	// 特定来源的内容加权
	if urlTag {
		rerankScore += 1
	}
	scoreDetail["urlTag"] = urlTag
	if !isMedical {
		rerankScore -= 1
	}
	scoreDetail["isMedical"] = isMedical

	// levelScore := CalcScore(common.Round(float64(data.AuthorScore), 2), data.DocLevel, data.Level)
	scoreDetail["level_socre"] = levelScore
	rerankScore += levelScore

	scoreDetail["score_with_level"] = rerankScore

	return rerankScore, scoreDetail
}

func (r *MedicalRerank) Rank1120(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, []*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()
	currentTime := time.Now().Unix() * 1000
	var category string
	var rerankScore = make([]*map[string]any, 0)
	domainId := req.Payload.DomainId
	id2ScoreDetail := make(map[int64]*map[string]any, 0)
	timeStrength := 1
	defer rankSpan.TraceInfo("scoreDetail", id2ScoreDetail)

	// 判断是否是强时间性query
	// 转换为小写并去除首尾空格
	query := strings.ToLower(strings.TrimSpace(data.Query))
	// 定义正则表达式模式
	pattern := regexp.MustCompile(consts.TimeStrengthReg)
	if len(pattern.FindAllString(query, -1)) > 0 {
		timeStrength = 3
	}

	addScore := 4
	mkb_tag := false

	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Recovered from panic:", r)
		}
	}()
	docs := data.Docs

	// // 分段，将结果按照精排模型分数按分段区分
	// sort.Slice(docs, func(i, j int) bool {
	// 	score1 := common.Interface2F64((*docs[i])["_rank_score"])

	// 	score2 := common.Interface2F64((*docs[j])["_rank_score"])

	// 	return score1 > score2
	// })
	var tmpData [][]*map[string]any

	tmpData = r.answerStage2(docs, req.Payload.ScoreThreshold, rankSpan)

	for _, datas := range tmpData {
		var tsList []int

		var rankScoreList []float64
		dataCount := len(datas)
		for _, v := range datas {
			postTs := common.Interface2I64((*v)["post_ts"])

			tsList = append(tsList, -int(postTs))

			score := common.Interface2F64((*v)["_rank_score"])
			rankScoreList = append(rankScoreList, float64(score))
		}

		// 统计时间重复率
		sort.Slice(tsList, func(i, j int) bool {
			return tsList[i] < tsList[j]
		})

		tsUnique := common.RemoveDuplicates(tsList)

		timeScore := common.Map(tsUnique, func(index int, item int) float64 {
			return 1 - math.Tanh(0.2*float64(index))
		})

		timeScoreDict := make(map[int]float64, dataCount)

		for i := 0; i < len(tsUnique); i++ {
			timeScoreDict[tsUnique[i]] = timeScore[i]
		}

		for _, d := range datas {
			_score := common.Interface2F64((*d)["_rank_score"])

			score, scoreDetail := r.rerankProcess(req, d, domainId, query, category, currentTime, _score, mkb_tag)
			scoreDetail["score_without_ts"] = score

			id2ScoreDetail[common.Interface2I64((*d)["id"])] = &scoreDetail
			postTs := common.Interface2I64((*d)["post_ts"])

			tsScore := timeScoreDict[-int(postTs)] * 0.0001 * float64(timeStrength)
			scoreDetail["ts"] = tsScore

			scoreDetail["add_score"] = float64(addScore)
			// scoreDetail["add_factor"] = float64(conf.AddDactor)
			scoreDetail["time_strength"] = float64(timeStrength)
			scoreDetail["time_score_dict"] = timeScoreDict

			score += tsScore + float64(addScore)
			scoreDetail["_rerank_score"] = score

			(*d)["_rerank_score"] = score
			rerankScore = append(rerankScore, d)

		}
		addScore = addScore - 3
	}

	return utils.FinalResultsProcess(rankSpan, req, rerankScore, true)
}
