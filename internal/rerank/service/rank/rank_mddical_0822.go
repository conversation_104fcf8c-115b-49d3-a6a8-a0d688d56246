package rank

import (
	"math"
	"sort"
	"strconv"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	concurrent_map "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/map"
)

// 对精排分数按相同语义分数水平分组
func GroupResults(docs []*map[string]any) [][]*map[string]any {
	// 所有 doc 的精排分数
	var rankScoreList []float64
	for _, doc := range docs {
		rankScore := common.Interface2F64((*doc)["_rank_score"])
		rankScoreList = append(rankScoreList, rankScore)
	}
	// 分割精排分数数组
	splitResult := splitArray(rankScoreList)

	// 创建与 splitResult 结构对应的二维结果列表
	var groupedResults [][]*map[string]any

	// 当前处理的结果索引
	currentIndex := 0

	// 遍历 splitResult 的每个分组
	for _, group := range splitResult {
		groupLength := len(group)
		var currentGroup []*map[string]any

		// 从原始结果中提取对应数量的元素
		for i := 0; i < groupLength; i++ {
			if currentIndex < len(docs) {
				currentGroup = append(currentGroup, docs[currentIndex])
				currentIndex++
			} else {
				break
			}
		}

		groupedResults = append(groupedResults, currentGroup)
	}

	return groupedResults
}

// 以下函数应产品要求单独作为可配置项, 例如库名可能会更改/增加
func (r *MedicalRerank) SortResults0822(results []*map[string]any, IndexCoedPriority *concurrent_map.ConcurrentMap[string, int]) {
	sort.Slice(results, func(i, j int) bool {
		data1, data2 := results[i], results[j]

		// 提取库名并获取优先级
		getPriority := func(lib string) int {

			if p, ok := IndexCoedPriority.Get(lib); ok {
				return p
			} else {
				return 0
			}
		}

		// 比较 library 优先级
		indexCode1 := common.Interface2S((*data1)["_indexCode"])
		indexCode2 := common.Interface2S((*data2)["_indexCode"])
		postTs1 := common.Interface2I64((*data1)["post_ts"])
		postTs2 := common.Interface2I64((*data2)["post_ts"])

		// library 相同时, post_ts 降序排列
		if postTs1 != postTs2 {
			return postTs1 > postTs2
		}

		if priority1, priority2 := getPriority(indexCode1), getPriority(indexCode2); priority1 != priority2 {
			return priority1 > priority2
		}

		// post_ts 相同时, rankScore 降序排列
		return common.Interface2F64((*data1)["_rank_score"]) > common.Interface2F64((*data2)["_rank_score"])
	})
}

func sortResultsByMedClass(queryType string, docs []*map[string]any) {
	// 为每个结果添加是否已被处理的标记
	processed := make([]bool, len(docs))

	// 从后向前遍历结果列表
	for i := len(docs) - 1; i >= 0; i-- {
		// 跳过已经处理过的数据
		if processed[i] {
			continue
		}

		current := docs[i]
		// 条件: Query和Doc的中医西医标签不同
		if queryType != common.Interface2S((*current)["medtype"]) {
			// 查找rankScore相差在0.08范围内且时间戳相差在126230400范围内的最后一个元素位置
			j := i
			for j+1 < len(docs) &&
				common.Interface2F64((*docs[i])["_rank_score"])-common.Interface2F64((*docs[j+1])["_rank_score"]) <= 0.08 &&
				common.Interface2I64((*docs[i])["post_ts"])-common.Interface2I64((*docs[j+1])["post_ts"]) <= 126230400 {
				j++
			}

			var newPos int
			// 计算新位置
			if common.Interface2F64((*docs[i])["_rank_score"]) >= 0.98 {
				newPos = i + 3 // 向后移动3位或到范围内末尾
			} else {
				newPos = i + 6 // 向后移动6位或到范围内末尾
			}

			// 确保不超过范围
			if newPos > j {
				newPos = j
			}

			// 检查移动路径上是否有已处理元素或DocClass相同的元素
			// 如果有, 将newPos设置为第一个已处理元素的前一个位置
			for k := i + 1; k <= newPos; k++ {
				if processed[k] || common.Interface2S((*docs[k])["medtype"]) == common.Interface2S((*current)["medtype"]) {
					newPos = k - 1
					break
				}
			}

			// 执行移动
			if newPos > i { // 只有需要移动时才执行
				// 保存要移动的元素
				movedResult := docs[i]

				// 将中间元素前移
				copy(docs[i:newPos], docs[i+1:newPos+1])

				// 将元素放到新位置
				docs[newPos] = movedResult

				// 标记该元素已被处理
				processed[newPos] = true

				// 更新其他已处理元素的索引
				for k := 0; k < i; k++ {
					if processed[k] && k > i && k <= newPos {
						processed[k-1] = true
						processed[k] = false
					}
				}
			}
		}
	}
}

// 排序: 处理匹配中医西医
func sortResultsByQualityScore(queryType string, docs []*map[string]any) {
	if len(docs) <= 1 {
		return // 空列表或只有一个元素无需排序
	}

	// 用于记录已执行插入操作的数据索引
	inserted := make(map[int]bool)

	// 从后向前遍历（不包括最后一个元素，因为后面没有元素可比较）
	for i := len(docs) - 2; i >= 0; i-- {

		current := docs[i]
		j := i + 1
		insertPos := -1

		// 从当前元素后面一个元素开始向后比较
		for j < len(docs) {
			// 跳过已执行插入操作的数据
			if inserted[j] {
				j++
				continue
			}
			// epsilon := 1e-6
			qScore1 := common.Interface2F64((*current)["q_score"])
			qScore2 := common.Interface2F64((*docs[j])["q_score"])
			diff := qScore2 - qScore1
			// 放大、四舍五入、缩小
			rounded := math.Round(diff*10) / 10
			// 检查是否满足停止条件
			if common.Interface2F64((*current)["_rank_score"])-common.Interface2F64((*docs[j])["_rank_score"]) > 0.08 ||
				common.Interface2F64((*current)["q_score"]) > common.Interface2F64((*docs[j])["q_score"]) ||
				rounded < 0.2 {
				// 找到插入位置
				insertPos = j
				break
			}

			j++
			insertPos = j
		}

		// 如果找到插入位置且不是紧挨着当前元素的位置，则执行插入
		if insertPos != -1 && insertPos != i+1 {
			// 移除当前元素
			element := docs[i]
			docs = append(docs[:i], docs[i+1:]...)

			// 插入到找到的位置
			docs = append(docs[:insertPos-1], append([]*map[string]any{element}, docs[insertPos-1:]...)...)

			// 记录该元素已执行插入操作
			inserted[insertPos] = true

			// 由于列表结构发生变化，需要调整索引
			// 因为我们是从后向前遍历，插入操作不会影响前面的索引，所以不需要调整i
		}
	}
}

func (r *MedicalRerank) Rank0822(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, []*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()

	// 中间过程明细，用于溯源
	// id2ScoreDetail := make(map[int64]*map[string]any)
	scoreDetails := make([]map[string]any, 0, len(data.Docs))
	defer func() {
		sort.Slice(scoreDetails, func(i, j int) bool {
			score1 := common.Interface2F64(scoreDetails[i]["_rerank_score"])
			score2 := common.Interface2F64(scoreDetails[j]["_rerank_score"])
			if score1 == score2 {
				rank_index1 := common.Interface2F64(scoreDetails[i]["_rank_score"])
				rank_index2 := common.Interface2F64(scoreDetails[j]["_rank_score"])
				return rank_index1 < rank_index2
			}
			return score1 > score2
		})
		rankSpan.TraceInfo("scoreDetail", scoreDetails)

	}()
	queryType := common.If(data.QueryType == "", "西医", data.QueryType)

	// 按相同语义分数水平分组
	groupDocs := GroupResults(data.Docs)
	var rerankScore = make([]*map[string]any, 0)

	// 构建权重map
	weight := len(req.Payload.RankCollections)
	IndexCodePriority := concurrent_map.New[string, int]()
	for _, indexCode := range req.Payload.RankCollections {
		IndexCodePriority.Set(indexCode, weight)
		weight--
	}

	for _, group := range groupDocs {
		var rerankScoreTemp = make([]*map[string]any, 0)

		for _, doc := range group {

			rankScore := common.Interface2F64((*doc)["_rank_score"])
			title := common.Interface2S((*doc)["title"])
			postTS := common.Interface2I64((*doc)["post_ts"])

			if postTS != 0 {
				// 线上时间戳为精确到毫秒的13位, 去除3位毫秒级, 保留前10位进行正常的Unix时间戳转换
				tsStr := strconv.FormatInt(postTS, 10)
				if len(string(tsStr)) > 10 {
					tsStr = tsStr[:10]
					postTS, _ = strconv.ParseInt(tsStr, 10, 64)
				}
			} else {
				postTS = extractYear(title) // 时间戳为0, 则从标题抽取时间信息
			}
			(*doc)["post_ts"] = postTS

			qualityScore := common.Interface2F64((*doc)["q_score"]) * 0.1
			(*doc)["_rerank_score"] = qualityScore + rankScore
			rerankScoreTemp = append(rerankScoreTemp, doc)

			//产品策略排序
			r.SortResults0822(rerankScoreTemp, IndexCodePriority)

			scoreDetail := map[string]any{
				"id":            common.Interface2I64((*doc)["id"]),
				"quality_score": qualityScore,
				"_rank_score":   rankScore,
				"_rerank_score": (*doc)["_rerank_score"],
			}
			scoreDetails = append(scoreDetails, scoreDetail)

		}
		rerankScore = append(rerankScore, rerankScoreTemp...)
	}
	// 最终排序(处理低质量文档)
	sortResultsByQualityScore(queryType, rerankScore)
	// 最终排序(处理中医西医文本)
	sortResultsByMedClass(queryType, rerankScore)

	return utils.FinalResultsProcess(rankSpan, req, rerankScore, false)
}
