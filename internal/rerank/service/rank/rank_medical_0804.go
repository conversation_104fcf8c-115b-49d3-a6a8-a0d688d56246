package rank

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strings"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	util "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/data_tool"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
	wrapper "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

func assembleToken(model string, queries []string, docs []*map[string]any) (map[string][]byte, map[string][]int64, error) {
	tokenMap := make(map[string][]byte)
	shapeMap := make(map[string][]int64)

	titles := make([]string, 0, len(docs))
	for _, doc := range docs {
		titles = append(titles, common.Interface2S((*doc)["title"]))
	}

	encodings, err := wrapper.EncodeBatchPair[int64](global.AseMap[model].Tokenizer.Wrapper, queries, titles, true)
	if err != nil {
		return nil, nil, err
	}

	for _, encoding := range encodings {
		tokenMap[consts.InputIds] = append(tokenMap[consts.InputIds], util.SliceToBytes(encoding.Ids)...)
		tokenMap[consts.AttentionMask] = append(tokenMap[consts.AttentionMask], util.SliceToBytes(encoding.Masks)...)
		tokenMap[consts.TokenType] = append(tokenMap[consts.TokenType], util.SliceToBytes(encoding.Types)...)
	}

	batchSize := int64(len(queries))
	shapeMap[consts.InputIds] = []int64{batchSize, int64(len(encodings[0].Ids))}
	shapeMap[consts.AttentionMask] = []int64{batchSize, int64(len(encodings[0].Masks))}
	shapeMap[consts.TokenType] = []int64{batchSize, int64(len(encodings[0].Types))}

	return tokenMap, shapeMap, nil
}

func isURLIncluded(url string, urls []string) bool {
	for _, site := range urls {
		if strings.Contains(url, site) {
			return true
		}
	}
	return false
}

// 产品策略: 调整包含特定URL的排序
func processURLs(docs []*map[string]any, req *bean.Request) {
	// 1. 补全URL字段
	for i := range docs {
		doc := docs[i]
		if common.Interface2S((*doc)["url"]) == "" {
			protocol := common.Interface2S((*doc)["protocal"])
			domain := common.Interface2S((*doc)["domain"])
			path := common.Interface2S((*doc)["path"])
			(*doc)["url"] = fmt.Sprintf("%s://%s%s", protocol, domain, path)
		}
	}

	processed := make(map[int]bool)
	urls := req.Payload.RankSites
	// collections := req.Payload.RankCollections
	// 步骤1: 处理包含 m.baidu.com/bh/m/detail/ar 的 URL
	for i := 0; i < len(docs); i++ {
		doc := docs[i]
		if isURLIncluded(common.Interface2S((*doc)["url"]), urls) {
			for j := 0; j < i; j++ {
				if processed[j] {
					continue
				}
				// 转换分数字段
				currentRank := common.Interface2F64((*doc)["_rank_score"])
				compareRank := common.Interface2F64((*docs[j])["_rank_score"])
				currentTitle := common.Interface2F64((*doc)["title_score"])
				compareTitle := common.Interface2F64((*docs[j])["title_score"])

				if math.Abs(compareRank-currentRank) <= 0.01 && math.Abs(compareTitle-currentTitle) <= 0.05 {
					moveElement(&docs, i, j)
					processed[j] = true
					break
				}
			}
			processed[i] = true
		}
	}
}

// 移动元素位置（适配map类型）
func moveElement(docs *[]*map[string]any, from, to int) {
	if from == to || from >= len(*docs) {
		return
	}

	doc := (*docs)[from]
	// 删除原位置元素
	*docs = append((*docs)[:from], (*docs)[from+1:]...)

	// 插入新位置
	if to >= len(*docs) {
		*docs = append(*docs, doc)
	} else {
		*docs = append(*docs, nil)
		copy((*docs)[to+1:], (*docs)[to:])
		(*docs)[to] = doc
	}
}

func infer(span *span.Span, req *bean.Request, query string, docs []*map[string]any) ([]float32, error) {
	opSpan := span.AddSpan("模型推理")
	defer opSpan.Finish()

	item := global.AseMap[req.Payload.Model]
	aseConf := item.AseConf
	if item == nil {
		return nil, selferrors.RerankError_ModelNotSupport.Detaild(fmt.Sprintf("invalid model: %s", req.Payload.Model))
	}
	var g errgroup.Group
	// 计算title_score
	// 按照指定batch_size重新划分doc
	var docsBatch [][]*map[string]any
	if len(docs) > 16 {
		docsBatch = lo.Chunk(docs[:16], aseConf.BatchSize)
	} else {
		docsBatch = lo.Chunk(docs, aseConf.BatchSize)
	}

	res := make([]float32, len(docs))
	for batchID, docs := range docsBatch {

		g.Go(func() error {
			batchSpan := opSpan.AddSpan("one-batch")
			// 调用模型推理
			tokenizerSpan := batchSpan.AddSpan("tokenizer")
			token, shape, err := assembleToken(req.Payload.Model, util.Repeat(query, len(docs)), docs)
			if err != nil {
				return err
			}
			tokenizerSpan.Finish()
			inferSpan := batchSpan.AddSpan("infer")
			// 调用模型推理
			client := item.AseSdk.Request().SetTraceId(req.Header.TraceId).SetParentSpan(inferSpan).SetHeaderTag(req.Header.Tag)
			req := &aseclient.InferReq{

				Inputs: token,
				Shapes: shape,
			}

			resp, err := client.Send(context.TODO(), req)
			if err != nil {
				return err
			}

			inferSpan.Finish()
			scores := util.BytesToSlice[float32](resp.Payload.Outputs[consts.LogitsName])

			for i, item := range scores {
				res[batchID*aseConf.BatchSize+i] = item
			}
			batchSpan.Finish()

			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}
	return res, nil
}

func (r *MedicalRerank) Rank0804(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, []*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()

	var (
		scoreDetails         = make([]map[string]any, len(data.Docs), len(data.Docs))
		rankScoreSortedDocs  = make([]*map[string]any, len(data.Docs))
		titleScoreSortedDocs = make([]*map[string]any, len(data.Docs))
		rerankScore          = make([]*map[string]any, len(data.Docs))
		scoreMap             = make(map[int64]float64)
	)

	defer func() {
		sort.Slice(scoreDetails, func(i, j int) bool {
			score1 := common.Interface2F64(scoreDetails[i]["_rerank_score"])
			score2 := common.Interface2F64(scoreDetails[j]["_rerank_score"])
			if score1 == score2 {
				rank_index1 := common.Interface2F64(scoreDetails[i]["_rank_index"])
				rank_index2 := common.Interface2F64(scoreDetails[j]["_rank_index"])
				return rank_index1 < rank_index2
			}
			return score1 > score2
		})
		rankSpan.TraceInfo("scoreDetail", scoreDetails)

	}()

	for i, doc := range data.Docs {
		rankScoreSortedDocs[i] = doc
		titleScoreSortedDocs[i] = doc
		rerankScore[i] = doc
	}

	titleScores, err := infer(rankSpan, req, data.Query, rerankScore)
	if err != nil {
		return nil, nil, err
	}
	for index, data := range rerankScore {
		if common.Interface2I64((*data)["_rank_index"]) > 16 {
			scoreMap[common.Interface2I64((*data)["id"])] = 0.0
		} else {
			scoreMap[common.Interface2I64((*data)["id"])] = float64(titleScores[index])
		}
	}
	rankSpan.TraceInfo("scoreMap", scoreMap)

	if len(data.Docs) >= 5 {
		// 按 rank_score 降序排列
		sort.Slice(rankScoreSortedDocs, func(i, j int) bool {
			score1 := common.Interface2F64((*rankScoreSortedDocs[i])["_rank_score"])
			score2 := common.Interface2F64((*rankScoreSortedDocs[j])["_rank_score"])
			return score1 > score2
		})

		// 按 title_score降序排列
		sort.Slice(titleScoreSortedDocs, func(i, j int) bool {
			score1, score2 := 0.0, 0.0
			if value, ok := scoreMap[common.Interface2I64((*titleScoreSortedDocs[i])["id"])]; ok {
				score1 = value
			} else {
				score1 = 0.0
			}
			if value, ok := scoreMap[common.Interface2I64((*titleScoreSortedDocs[j])["id"])]; ok {
				score2 = value
			} else {
				score2 = 0.0
			}
			return score1 > score2
		})

		// 比较前5名的 id
		rankTop5 := make([]int64, 0)
		for _, doc := range rankScoreSortedDocs[:5] {
			rankTop5 = append(rankTop5, common.Interface2I64((*doc)["id"]))
		}

		titleTop5 := make([]int64, 0)
		for _, doc := range titleScoreSortedDocs[:5] {
			titleTop5 = append(titleTop5, common.Interface2I64((*doc)["id"]))
		}

		// 计算不同 id 的数量
		diffCount := 0
		for _, id := range titleTop5 {
			if !common.Contains(rankTop5, id) {
				diffCount++
			}
		}

		// 如果不同 id 数量大于等于4, 则全部 title_score 置 0
		if diffCount >= 4 {
			for i := range rerankScore {
				(*rerankScore[i])["title_score"] = 0.0
				scoreMap[common.Interface2I64((*rerankScore[i])["id"])] = 0.0
			}
		}
	}

	// 步骤2: 计算 rerank_score 并降序排列
	for i := range rerankScore {
		id := common.Interface2I64((*rerankScore[i])["id"])
		// 如果 id 在 scoreMap 中不存在，则设置为 0
		titleScore := 0.0
		if _, ok := scoreMap[id]; ok {
			titleScore = scoreMap[id]
		}
		(*rerankScore[i])["title_score"] = titleScore
		rankScore := common.Interface2F64((*rerankScore[i])["_rank_score"])
		docLevel := 1
		if t, ok := (*rerankScore[i])["levels"].(map[string]any); ok {
			for key, value := range t {
				if common.Interface2S(key) == config.G_ParamModule.DefaultInstance().HealthLevel {
					docLevel = int(common.Interface2I64(value))
				}

			}
		} else {
			docLevel = 1
		}

		(*rerankScore[i])["_rerank_score"] = (rankScore-0.75)*10 + titleScore + float64(docLevel)*0.04

		scoreDetail := map[string]any{
			"_id":           id,
			"_rank_score":   common.Interface2F64((*rerankScore[i])["_rank_score"]),
			"title_score":   titleScore,
			"level_score":   float64(docLevel) * 0.04,
			"_rerank_score": common.Interface2F64((*rerankScore[i])["_rerank_score"]),
		}
		scoreDetails[i] = scoreDetail

	}

	sort.Slice(rerankScore, func(i, j int) bool {
		rerankScoreI := common.Interface2F64((*rerankScore[i])["_rerank_score"])
		rerankScoreJ := common.Interface2F64((*rerankScore[j])["_rerank_score"])
		if rerankScoreI == rerankScoreJ {
			return common.Interface2F64((*rerankScore[i])["_rank_index"]) < common.Interface2F64((*rerankScore[j])["_rank_index"])
		}
		return rerankScoreI > rerankScoreJ
	})

	processURLs(rerankScore, req)

	return utils.FinalResultsProcess(rankSpan, req, rerankScore, false)
}
