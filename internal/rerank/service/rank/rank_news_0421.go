package rank

import (
	"fmt"
	"math"
	"slices"
	"strings"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
)

type NewsRerank struct {
	Ctx *pandora_context.PandoraContext
}

// longestSubstring 计算两个字符串的最长公共子串
func longestSubstring(str1, str2 string) string {
	s1 := []rune(str1)
	s2 := []rune(str2)
	m := len(s1)
	n := len(s2)
	// 创建一个二维数组来记录公共子串的长度
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}
	maxLength := 0
	endIndex := 0
	// 填充二维数组
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if s1[i-1] == s2[j-1] {
				dp[i][j] = dp[i-1][j-1] + 1
				if dp[i][j] > maxLength {
					maxLength = dp[i][j]
					endIndex = i - 1
				}
			} else {
				dp[i][j] = 0
			}
		}
	}
	// 提取最长公共子串
	if maxLength == 0 {
		return ""
	}
	startIndex := endIndex - maxLength + 1
	return string(s1[startIndex : endIndex+1])
}

// matchWordsScore 计算关键词匹配得分
func MatchWordsScore(span *span.Span, id int64, query string, title string, content string, queryKeysOrigin []string, queryValuesOrigin []float64) (float64, []string, []string) {
	opSpan := span.AddSpan("关键词匹配")
	defer opSpan.Finish()
	wordsScore := 0.0
	queryLen := len(queryKeysOrigin)
	queryTag := make([]bool, queryLen)
	matchDictTitle := []string{}
	matchDictContent := []string{}

	// 深拷贝
	queryKeys := make([]string, len(queryKeysOrigin))
	copy(queryKeys, queryKeysOrigin)
	queryValues := make([]float64, len(queryValuesOrigin))
	copy(queryValues, queryValuesOrigin)

	// 直接匹配, 或同义词匹配, 分为匹配 title 和 content
	for i := 0; i < queryLen; i++ {
		if !queryTag[i] {
			if strings.Contains(title, queryKeys[i]) || utils.SynonymMatch(queryKeys[i], title, config.G_ResModule.DefaultInstance().SynonymsDict) {
				wordsScore += queryValues[i] * 1.0
				queryTag[i] = true
				matchDictTitle = append(matchDictTitle, queryKeys[i])
			} else if strings.Contains(content, queryKeys[i]) || utils.SynonymMatch(queryKeys[i], title, config.G_ResModule.DefaultInstance().SynonymsDict) {
				wordsScore += queryValues[i] * 0.98
				queryTag[i] = true
				matchDictContent = append(matchDictContent, queryKeys[i])
			}
		}
	}

	// 最长公共子串, 按子串长度得部分分数
	for i := 0; i < queryLen; {
		if !queryTag[i] {
			substring := longestSubstring(queryKeys[i], title)
			if substring != "" {
				wordsScore += queryValues[i] * float64(len([]rune(substring))) / float64(len([]rune(queryKeys[i]))) * 1.0
				matchDictTitle = append(matchDictTitle, substring)
				queryValues[i] *= (1 - float64(len([]rune(substring)))/float64(len([]rune(queryKeys[i]))))
				queryKeys[i] = strings.ReplaceAll(queryKeys[i], substring, "")
				if queryKeys[i] != "" {
					i = 0
				} else {
					queryTag[i] = true
					i++
				}
			} else {
				i++
			}
		} else {
			i++
		}
	}
	return wordsScore, matchDictTitle, matchDictContent // matchDictTitle, matchDictContent 仅仅用于保存到 excel 查看, 不参与其他
}

// matchPositionsScore 计算地点匹配得分
func MatchPositionsScore(span *span.Span, id int64, positionsList []string, title, content string) (float64, []string) {
	opSpan := span.AddSpan("地点匹配")
	defer opSpan.Finish()
	matchDictPosition := []string{}
	positionsScore := 1.0
	for _, position := range positionsList {
		foundInTitle := false
		foundInContent := false
		for i := 0; i < len(title); i++ {
			if len(title)-i >= len(position) && title[i:i+len(position)] == position {
				foundInTitle = true
				break
			}
		}
		for i := 0; i < len(content); i++ {
			if len(content)-i >= len(position) && content[i:i+len(position)] == position {
				foundInContent = true
				break
			}
		}
		if !foundInTitle && !foundInContent {
			positionsScore = 0
		} else if !foundInTitle {
			positionsScore = 0.98
			matchDictPosition = append(matchDictPosition, position)
		} else {
			positionsScore = 1.0
			matchDictPosition = append(matchDictPosition, position)
		}
	}
	return positionsScore, matchDictPosition // matchDictPosition 仅仅用于保存到 excel 查看, 不参与其他
}

// timeCompare 检查 titleList 中的元素是否都在 queryList 中。
// 如果 queryList 为空，返回 false；否则，遍历 titleList，若有元素不在 queryList 中，返回 false，否则返回 true。
func timeCompare(queryList []int, titleList []int) bool {
	// 若 queryList 为空，无法进行匹配，返回 false
	if len(queryList) == 0 {
		return false
	}
	// 遍历 titleList 中的每个元素
	for _, element := range titleList {
		// 检查当前元素是否不在 queryList 中
		if !slices.Contains(queryList, element) {
			return false
		}
	}
	return true
}

// compareDateComponents 比较日期组件（年、月、日）。
// 若 query 或 title 的年、月、日列表都为空，返回 false；否则，调用 timeCompare 函数分别比较年、月、日，全部匹配则返回 true。
func compareDateComponents(queryYears, queryMonths, queryDays, titleYears, titleMonths, titleDays []int) bool {
	// 若 query 或 title 的年、月、日列表都为空，无法进行比较，返回 false
	if (len(queryYears) == 0 && len(queryMonths) == 0 && len(queryDays) == 0) ||
		(len(titleYears) == 0 && len(titleMonths) == 0 && len(titleDays) == 0) {
		return false
	}
	// 分别比较年、月、日，全部匹配则返回 true
	return timeCompare(queryYears, titleYears) && timeCompare(queryMonths, titleMonths) && timeCompare(queryDays, titleDays)
}

// MatchTimesScore 计算时间匹配得分。
// 通过时间戳、时间词、日期组件和年份等维度进行判断，最终返回时间匹配得分。
func MatchTimesScore(span *span.Span, id int64, timesList []string, postTs int64, startTimeStamp int64, endTimeStamp int64, query string, title string, content string, timesScoreDict map[int64]float64) float64 {
	opSpan := span.AddSpan("时间匹配")
	defer opSpan.Finish()

	// 时间戳判断
	// 初始化时间匹配得分，默认值为 1.0，表示完全匹配
	timesScore := 1.0
	// 判断文章发布时间是否晚于查询结束时间
	postAfterEnd := postTs > endTimeStamp
	// 判断文章发布时间是否早于查询开始时间
	postBeforeStart := postTs < startTimeStamp

	// 若文章发布时间晚于查询结束时间，将时间匹配得分设为 0
	if postAfterEnd {
		timesScore = 0
	} else if postBeforeStart {
		// 若文章发布时间早于查询开始时间，将时间匹配得分设为 -1
		timesScore = -1
	}

	// 时间词判断
	// 再次检查文章发布时间是否晚于查询结束时间
	if postTs > endTimeStamp {
		// 遍历时间词列表
		for _, timeStr := range timesList {
			// 检查文章标题是否包含当前时间词
			if !strings.Contains(title, timeStr) {
				// 若标题不包含当前时间词，将时间匹配得分设为 0，并跳出循环
				timesScore = 0
				break
			}
			// 若标题包含当前时间词，将时间匹配得分设为 1
			timesScore = 1
		}
	}

	// 提取时间范围和时间信息并比较
	// 从查询中提取时间范围的年、月、日
	queryYears, queryMonths, queryDays := common.ExtractRangeTime(query)
	// 从文章标题中提取时间范围的年、月、日
	titleYears, titleMonths, titleDays := common.ExtractRangeTime(title)
	// 比较查询和标题的时间范围信息，若匹配则将时间匹配得分设为 1
	if compareDateComponents(queryYears, queryMonths, queryDays, titleYears, titleMonths, titleDays) {
		timesScore = 1
	}

	// 从文章标题中提取具体时间的年、月、日
	titleYears, titleMonths, titleDays = common.ExtractTime(title)
	// 比较查询的时间范围和标题的具体时间信息，若匹配则将时间匹配得分设为 1
	if compareDateComponents(queryYears, queryMonths, queryDays, titleYears, titleMonths, titleDays) {
		timesScore = 1
	}

	// 从查询中提取具体时间的年、月、日
	queryYears, queryMonths, queryDays = common.ExtractTime(query)
	// 比较查询和标题的具体时间信息，若匹配则将时间匹配得分设为 1
	if compareDateComponents(queryYears, queryMonths, queryDays, titleYears, titleMonths, titleDays) {
		timesScore = 1
	}

	// 年份判断
	// 从查询中提取年份列表
	queryYear := common.ExtractYearsList(query)
	// 从文章标题中提取年份列表
	titleYear := common.ExtractYearsList(title)
	// 若标题中只提取到一个年份
	if len(titleYear) == 1 {
		// 若查询中只有一个年份且与标题中的年份不同，或者查询中没有年份且标题中的年份与当前年份不同，将时间匹配得分设为 -1
		if (len(queryYear) == 1 && queryYear[0] != titleYear[0]) ||
			(len(queryYear) == 0 && titleYear[0] != int(time.Now().Year())) {
			timesScore = -1
		}
	}

	// 上述步骤均为置-1/0/1判断，仅>0可获得时间得分
	// 时间得分强度，用于调整最终得分
	timeStrength := 1.0
	// 若时间匹配得分大于 0，根据时间得分字典和时间强度计算最终得分
	if timesScore > 0 {
		timesScore = timesScoreDict[-postTs] * 0.3 * timeStrength
	}
	return timesScore
}

// 处理 query, keysList, positionsList, timesList, startTimesList, endTimesList 均由上游链路传入
func processQuery(req *bean.Request, span *span.Span, query string, keysList []string, positionsList []string, timesList []string, startTimesList []string, endTimesList []string) ([]string, []float64, []string, []string, int64, int64, error) {
	opSpan := span.AddSpan("query处理")
	defer opSpan.Finish()
	// 关键词处理
	keysList = utils.RemoveWords(keysList, config.G_ResModule.DefaultInstance().BlockDict)
	var valuesList = make([]float64, len(keysList))
	if len(keysList) > 0 {
		keysList, vList, err := utils.TermWeight(req, opSpan, keysList)
		if err != nil {
			return keysList, vList, positionsList, timesList, 0, 0, err
		}
		for i := range vList {
			value := float64(math.Round(float64(vList[i]*1000)) / 1000)
			valuesList[i] = float64(int(value*1000)) / 1000
		}
	} else {
		valuesList = []float64{}
	}

	// 地点词处理
	positionsList = utils.RemoveAffix(positionsList, config.G_ResModule.DefaultConfig().PrefixDict, config.G_ResModule.DefaultConfig().SuffixDict)

	// 时间词处理
	timesList = utils.RemoveAffix(timesList, config.G_ResModule.DefaultConfig().PrefixDict, config.G_ResModule.DefaultConfig().SuffixDict)

	// 时间戳处理
	var startTime, endTime string
	if len(startTimesList) > 0 {
		// 找到最大值
		startTime = startTimesList[0]
		for _, t := range startTimesList {
			if t > startTime {
				startTime = t
			}
		}
	} else {
		startTime = "1970-01-01 00:00:00"
	}
	if len(endTimesList) > 0 {
		// 找到最小值
		endTime = endTimesList[0]
		for _, t := range endTimesList {
			if t < endTime {
				endTime = t
			}
		}
	} else {
		endTime = "2099-12-31 23:59:59"
	}

	layout := "2006-01-02 15:04:05"
	// 定义北京时间时区，使用固定偏移量 +8 小时（28800 秒）
	beijing := time.FixedZone("CST", 8*60*60) // CST 是中国标准时间的缩写

	// startTimeStamp, err := time.Parse(layout, startTime)
	startTimeStamp, err := time.ParseInLocation(layout, startTime, beijing)
	if err != nil {
		fmt.Println("Error parsing start time:", err)
	}
	// endTimeStamp, err := time.Parse(layout, endTime)
	endTimeStamp, err := time.ParseInLocation(layout, endTime, beijing)
	if err != nil {
		fmt.Println("Error parsing end time:", err)
	}
	return keysList, valuesList, positionsList, timesList, startTimeStamp.Unix(), endTimeStamp.Unix(), nil
}

// 排序函数
func sortSlice(slice []int64) {
	for i := 0; i < len(slice)-1; i++ {
		for j := 0; j < len(slice)-i-1; j++ {
			if slice[j] > slice[j+1] {
				slice[j], slice[j+1] = slice[j+1], slice[j]
			}
		}
	}
}

// 多样性结果排序函数
func sortResultsWithDiversity(results []*map[string]any, topicListMap map[int64][]string) {
	if len(results) <= 1 {
		return
	}

	// 处理topicList逻辑
	num := 1
	for i := 1; i < len(results); i++ {
		if num >= 5 {
			break
		}

		Intersection := false
		// 检查与前num个results是否有交集
		for j := 0; j < num; j++ {
			id1 := common.Interface2I64((*results[i])["id"])
			id2 := common.Interface2I64((*results[j])["id"])
			topicList1 := topicListMap[id1]
			topicList2 := topicListMap[id2]
			if TopicIntersection(topicList1, topicList2) {
				Intersection = true
				break
			}
		}

		// 如果没有交集，移动到num+1位置
		if !Intersection {
			// 保存当前元素
			current := results[i]
			// 将中间元素后移
			for k := i; k > num; k-- {
				results[k] = results[k-1]
			}
			// 放入新位置
			results[num] = current
			num++
		}
	}
}

// 判断两个topicList是否有交集
func TopicIntersection(list1, list2 []string) bool {
	set := make(map[string]bool)
	for _, topic := range list1 {
		set[topic] = true
	}
	for _, topic := range list2 {
		if set[topic] {
			return true
		}
	}
	return false
}

func (r *NewsRerank) Rank20250421(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, []*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()
	query := data.Query
	docs := data.Docs
	isDomainRecall := data.DomainRecall

	var rerankScore = make([]*map[string]any, 0)
	id2ScoreDetail := make(map[int64]*map[string]any, 0)
	defer rankSpan.TraceInfo("scoreDetail", id2ScoreDetail)
	docTopicListMap := make(map[int64][]string)

	qKeywords := data.QKeywords
	positionsList := qKeywords.OKeyWords.Location
	keysList := append(qKeywords.OKeyWords.CoreWord, qKeywords.OKeyWords.StrongReq...)

	// 时间戳
	var startTimesList, endTimesList []string
	var timesList []string
	for _, timeInfo := range qKeywords.NKeyWords.Time {
		if timeInfo.TimeType == "accurate" {
			startTimesList = append(startTimesList, timeInfo.StartTime)
			endTimesList = append(endTimesList, timeInfo.EndTime)
			timesList = append(timesList, timeInfo.Raw)
		}
	}

	// query 信息处理
	keysList, valuesList, positionsList, timesList, startTimeStamp, endTimeStamp, err := processQuery(req, rankSpan, query, keysList, positionsList, timesList, startTimesList, endTimesList)
	if err != nil {
		if req.Payload.Full {
			return data.Docs, data.Docs, err
		}
		return data.Docs, nil, err
	}

	// 所有 doc 的网页时间戳
	var timesStampList []int64
	for _, doc := range docs {
		postTs := common.Interface2I64((*doc)["post_ts"])
		timesStampList = append(timesStampList, -postTs)
	}

	// 统计时间的重复率
	timesStampUnique := make(map[int64]bool)
	var timesSort []int64
	for _, ts := range timesStampList {
		if !timesStampUnique[ts] {
			timesSort = append(timesSort, ts)
			timesStampUnique[ts] = true
		}
	}
	sortSlice(timesSort)
	timesScore := make([]float64, len(timesSort))
	for i := range timesSort {
		timesScore[i] = 1 - math.Tanh(0.05*float64(i))
	}
	// 时间戳与时间得分一一对应的字典
	timesScoreDict := make(map[int64]float64)
	for i, ts := range timesSort {
		timesScoreDict[ts] = timesScore[i]
	}
	opSpan := rankSpan.AddSpan("doc打分")

	for i, doc := range docs {

		var scoreDetail = &map[string]any{}
		id := common.Interface2I64((*doc)["id"])
		title := strings.ToUpper(strings.ReplaceAll(common.Interface2S((*doc)["title"]), " ", ""))
		content := strings.ReplaceAll(common.Interface2S((*doc)["content"]), "\x00", " ")
		content = strings.ReplaceAll(content, "\u001b", " ")
		content = strings.ReplaceAll(content, "\u0000", " ")
		postTS := int64(common.Interface2I64((*doc)["post_ts"]))

		if isDomainRecall {
			topicList := utils.ExtractKeywords(title, content)
			docTopicListMap[id] = topicList
			(*scoreDetail)["topic_list"] = topicList
		}

		// 关键词得分
		wordsScore, matchDictTitle, matchDictContent := MatchWordsScore(opSpan, id, query, title, content, keysList, valuesList)

		// 地点词得分
		positionsScore, matchDictPosition := MatchPositionsScore(opSpan, id, positionsList, title, content)

		// 时间词得分
		timesScore := MatchTimesScore(opSpan, id, timesList, postTS, startTimeStamp, endTimeStamp, query, title, content, timesScoreDict)

		// 最终得分
		finScore := wordsScore + timesScore + positionsScore

		// 记录分数
		(*scoreDetail)["words_score"] = wordsScore
		(*scoreDetail)["positions_score"] = positionsScore
		(*scoreDetail)["times_score"] = timesScore
		(*scoreDetail)["_rerank_score"] = finScore
		(*scoreDetail)["match_dict_title"] = matchDictTitle
		(*scoreDetail)["match_dict_content"] = matchDictContent
		(*scoreDetail)["match_dict_position"] = matchDictPosition

		id2ScoreDetail[id] = scoreDetail

		// 只有当时间得分>=0，才进行输出
		if timesScore >= 0 {
			(*doc)["_rerank_score"] = finScore
			(*doc)["_rerank_index"] = i + 1
			rerankScore = append(rerankScore, doc)
		}

	}
	opSpan.Finish()
	limit := req.Payload.TopK
	req.Payload.TopK = -1
	sortedDocs, fullDocs, err := utils.FinalResultsProcess(rankSpan, req, rerankScore, true)
	if err != nil {
		return sortedDocs, fullDocs, err
	}

	if isDomainRecall && req.Payload.Model == consts.News_V20250522 {
		sortResultsWithDiversity(sortedDocs, docTopicListMap)
	}

	if limit >= 0 && limit <= len(sortedDocs) {
		if req.Payload.Full {
			return sortedDocs[:limit], fullDocs, nil
		}
		return sortedDocs[:limit], nil, nil
	} else {
		if req.Payload.Full {
			return sortedDocs[:limit], fullDocs, nil
		}
		return sortedDocs, nil, nil
	}

}
