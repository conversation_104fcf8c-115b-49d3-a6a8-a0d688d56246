package consts

var (
	MEDICAL        = "1"
	HEALTH         = "5001"
	V1120          = "5001_V1120"
	V1218          = "5001_V1218"
	Medical_V0310  = "5001_V20250310"
	Medical_V1120  = "5001_V20251120"
	Medical_V1218  = "5001_V20251218"
	Medical_V0516  = "5001_V20250516"
	Medical_V0620  = "5001_V20250620"
	Medical_V0804  = "5001_V20250804"
	Medical_V0822  = "5001_V20250822"
	HISTORY        = "5007"
	SPORTS         = "5010"
	News_V20250421 = "news_V20250421"
	News_V20250522 = "news_V20250522"
)

const TimeStrengthReg = "最新|动态|现实情况|新闻|最近新闻|今天|今年|现在|现状|现任|价格|新消息"

var VideoKeywords = map[string]bool{
	"高清完整": false,
	"在线播放": false,
}

var QualityScore = map[int]float64{
	4: 1,
	3: 0.7,
	2: 0.3,
	1: 0,
	0: 0,
}

var LevelScoreDict = map[int]float64{
	4: 3,
	3: 2,
	2: 1,
	1: 0,
}

var Biaodians = map[rune]bool{
	';': true, '＇': true, '∶': true, '…': true, '！': true, '\n': true,
	'，': true, '：': true, '？': true, '；': true, '‼': true, '。': true,
	'、': true, '?': true, '!': true, ',': true, '.': true, '～': true,
	'–': true, '\t': true,
}

var SpecialChars = map[rune]bool{
	'↓': true, 'Ⅰ': true, '◎': true, '》': true, ']': true,
	'『': true, '⭐': true, '―': true, '］': true, '�': true,
	'\\': true, '©': true, '{': true, '\r': true, '_': true,
	'>': true, '℃': true, '~': true, '《': true, '﹙': true,
	'’': true, '）': true, '•': true, '【': true, '×': true,
	'⊙': true, '[': true, '<': true, '#': true, '」': true,
	'［': true, '“': true, '（': true, '】': true, '\xa0': true,
	'→': true, '「': true, '@': true, '`': true, '′': true,
	'|': true, '➤': true, '／': true, '〔': true, '·': true,
	':': true, '(': true, '★': true, '°': true, '◆': true,
	'‘': true, '‰': true, '▲': true, '≥': true, ')': true,
	'＞': true, '\u2002': true, '\'': true, '√': true, '❤': true,
	'″': true, '&': true, '●': true, '』': true, '"': true,
	'・': true, '}': true, '❃': true, '△': true, '＂': true,
	'—': true, '%': true, '〕': true, '❀': true, '﹚': true,
	'．': true, '”': true, '\x05': true, '＜': true, '\u3000': true,
}

var (
	NEWS      = "news"
	HOTTOPIC  = "hot_topic"
	HOTSEARCH = "hot_search"
)

var (
	STRONG_TIMELINESS = "0"
	MEDIUM_TIMELINESS = "1"
	WEAK_TIMELINESS   = "2"
)

var HistoryToday string = "201"

var Date_baikeids []int64

var TimelinessLabel = []string{"0", "1", "2"}

var IntentLabel = []string{NEWS, HOTSEARCH, HOTTOPIC}
var CLS int64 = 1
var SEP int64 = 2
var TYPES int64 = 0
var MASK int64 = 1

const (
	LogitsName string = "logits"

	// KeyTag tlbTag
	KeyTag string = "tlb_tag"

	TraceId string = "traceId"

	// InputIds 模型输入token
	InputIds string = "input_ids"
	// AttentionMask 模型输入mask
	AttentionMask string = "attention_mask"
	// TokenType 模型输入
	TokenType string = "token_type_ids"
)
