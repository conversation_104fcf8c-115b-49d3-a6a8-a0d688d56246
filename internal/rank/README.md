# Rank Service Documentation

## 概述

Rank Service是lynxiao-ai-search项目中的精排服务，负责对搜索结果进行精确排序和评分。该服务基于机器学习模型对查询和文档进行相关性计算，为用户提供高质量的搜索结果。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────────┐
│                        Rank Service Architecture                    │
├─────────────────────────────────────────────────────────────────────┤
│  ┌──────────────┐    ┌────────────────┐    ┌─────────────────────┐  │
│  │  HTTP API    │    │  Validation    │    │  Span Tracing       │  │
│  │  /rank/api/v2│───▶│  Layer         │───▶│  & Monitoring       │  │
│  └──────────────┘    └────────────────┘    └─────────────────────┘  │
│                                ▼                                    │
│  ┌─────────────────────────────────────────────────────────────────┐  │
│  │                    Service Layer                                │  │
│  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────────────┐  │  │
│  │  │   Process    │  │   Process2   │  │   Rank Inference     │  │  │
│  │  │   (Normal)   │  │   (Chunk)    │  │   Engine             │  │  │
│  │  └──────────────┘  └──────────────┘  └──────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────────┘  │
│                                ▼                                    │
│  ┌─────────────────────────────────────────────────────────────────┐  │
│  │                  Model & Tokenizer Layer                       │  │
│  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────────────┐  │  │
│  │  │  Tokenizer   │  │  ASE Model   │  │  Batch Processing    │  │  │
│  │  │  Engine      │  │  Inference   │  │  & Scoring           │  │  │
│  │  └──────────────┘  └──────────────┘  └──────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────────┘  │
│                                ▼                                    │
│  ┌─────────────────────────────────────────────────────────────────┐  │
│  │                    Post-Processing Layer                       │  │
│  │  ┌──────────────┐  ┌──────────────┐  ┌──────────────────────┐  │  │
│  │  │   Sorting    │  │ Deduplication│  │   Threshold          │  │  │
│  │  │   & Ranking  │  │   & Merge    │  │   Filtering          │  │  │
│  │  └──────────────┘  └──────────────┘  └──────────────────────┘  │  │
│  └─────────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────┘
```

### 数据流架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │───▶│   API       │───▶│  Validation │───▶│   Service   │
│   Request   │    │   Layer     │    │   Layer     │    │   Layer     │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                                 │
                                                                 ▼
┌─────────────────────────────────────────────────────────────────────┐
│                         Parallel Processing                        │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────────────────┐  │
│  │   Query 1    │  │   Query 2    │  │        Query N           │  │
│  │              │  │              │  │                          │  │
│  │  ┌─────────┐ │  │  ┌─────────┐ │  │  ┌─────────────────────┐ │  │
│  │  │ Expr    │ │  │  │ Expr    │ │  │  │ Expr Evaluation     │ │  │
│  │  │ Eval    │ │  │  │ Eval    │ │  │  │ & Content Extract   │ │  │
│  │  └─────────┘ │  │  └─────────┘ │  │  └─────────────────────┘ │  │
│  │      │       │  │      │       │  │           │              │  │
│  │  ┌─────────┐ │  │  ┌─────────┐ │  │  ┌─────────────────────┐ │  │
│  │  │Tokenize │ │  │  │Tokenize │ │  │  │ Tokenization        │ │  │
│  │  └─────────┘ │  │  └─────────┘ │  │  └─────────────────────┘ │  │
│  │      │       │  │      │       │  │           │              │  │
│  │  ┌─────────┐ │  │  ┌─────────┐ │  │  ┌─────────────────────┐ │  │
│  │  │ Model   │ │  │  │ Model   │ │  │  │ Model Inference     │ │  │
│  │  │ Infer   │ │  │  │ Infer   │ │  │  │ & Scoring           │ │  │
│  │  └─────────┘ │  │  └─────────┘ │  │  └─────────────────────┘ │  │
│  └──────────────┘  └──────────────┘  └──────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────────┐
│                        Result Aggregation                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────────┐  │
│  │   Merge     │  │   Sort      │  │   Filter & Deduplicate      │  │
│  │  Results    │  │ by Score    │  │   Apply Threshold           │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
                            ┌─────────────┐
                            │   Final     │
                            │   Response  │
                            └─────────────┘
```

## 核心组件

### 1. API Layer (api.go)

**职责**: 
- 接收HTTP请求并进行初步处理
- 参数验证和格式化
- 链路追踪和监控
- 错误处理和响应格式化

**关键功能**:
- `RankHandler`: 主要的HTTP处理函数
- `fillHeader`: 统一错误响应格式
- `sonicUnmarshal`: 高性能JSON反序列化

### 2. Service Layer (service/rank_service.go)

**职责**:
- 核心业务逻辑处理
- 模型推理管理
- 并发处理和性能优化
- 结果后处理

**关键组件**:

#### Service结构体
```go
type service struct {
    aseMap   map[string]*aseItem  // 模型配置映射
    rankExpr *util.RankExpr       // 排序表达式引擎
}
```

#### AseItem结构体
```go
type aseItem struct {
    aseConf   *entity.ModelConfig      // 模型配置
    aseSdk    *aseclient.InferClient   // 推理客户端
    tokenizer *wrapper.AsyncTokenizer  // 分词器
}
```

### 3. Entity Layer (entity/)

**职责**:
- 常量定义
- 数据结构定义
- 配置管理

**关键组件**:
- `const.go`: 系统常量定义
- `custom.go`: 自定义配置结构

## 主要处理流程

### 1. 标准模式处理流程

```
┌─────────────────┐
│  接收请求        │
│  req.Payload    │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  数据验证        │
│  & 过滤         │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  并发处理        │
│  多个Query      │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  表达式计算      │
│  提取排序内容    │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  文本分词        │
│  Token化        │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  批量推理        │
│  模型计算分数    │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  分数赋值        │
│  & 阈值过滤     │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  结果排序        │
│  & 去重         │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  TopK截断        │
│  & 索引标记     │
└─────────────────┘
```

### 2. 分块模式处理流程

分块模式专门处理长文档，将文档分割成多个片段分别计算相关性得分。

```
┌─────────────────┐
│  文档分块        │
│  chunk处理      │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  片段独立计算    │
│  相关性得分      │
└─────────────────┘
          │
          ▼
┌─────────────────┐
│  取最高分作为    │
│  文档最终得分    │
└─────────────────┘
```

## 用例图

```
                    ┌─────────────────────────────────────────┐
                    │                Rank Service             │
                    └─────────────────────────────────────────┘
                                      │
    ┌─────────────────────────────────┼─────────────────────────────────┐
    │                                 │                                 │
    ▼                                 ▼                                 ▼
┌─────────────┐                 ┌─────────────┐                 ┌─────────────┐
│   Search    │                 │   Rerank    │                 │   Batch     │
│   Client    │                 │   Service   │                 │   Process   │
└─────────────┘                 └─────────────┘                 └─────────────┘
    │                                 │                                 │
    │ 1. 提交搜索结果                   │ 1. 重新排序结果                   │ 1. 批量处理请求
    │ 进行精排                         │                                 │
    │                                 │                                 │
    ▼                                 ▼                                 ▼
┌─────────────────────────────────────────────────────────────────────────┐
│                         Use Cases                                      │
│                                                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────┐  │
│  │   UC1: 标准     │  │   UC2: 分块     │  │   UC3: 查询改写         │  │
│  │   文档排序      │  │   文档排序      │  │   结果合并              │  │
│  │                 │  │                 │  │                         │  │
│  │ - 接收查询和文档 │  │ - 长文档分块处理 │  │ - 原始查询和改写查询    │  │
│  │ - 计算相关性得分 │  │ - 片段独立计算   │  │ - 结果合并和去重        │  │
│  │ - 排序和过滤     │  │ - 取最高分数     │  │ - 统一排序输出          │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────────────┘  │
│                                                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────┐  │
│  │   UC4: 批量     │  │   UC5: 阈值     │  │   UC6: 多模型           │  │
│  │   并发处理      │  │   过滤          │  │   支持                  │  │
│  │                 │  │                 │  │                         │  │
│  │ - 多查询并发     │  │ - 设置最低分数   │  │ - 支持多种排序模型      │  │
│  │ - 批量模型推理   │  │ - 过滤低质量结果 │  │ - 动态模型选择          │  │
│  │ - 结果聚合       │  │ - 提升结果质量   │  │ - 模型配置管理          │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────────┘
```

## 性能优化

### 1. 内存优化
- **预分配切片容量**: 根据预期数据量预分配slice和map容量
- **对象池复用**: 重用频繁分配的对象
- **减少内存拷贝**: 使用指针和引用避免不必要的数据拷贝

### 2. 并发优化
- **并发限制**: 使用errgroup.SetLimit控制并发goroutine数量
- **批量处理**: 按batch_size分批处理请求，提高吞吐量
- **异步处理**: 使用goroutine并发处理多个查询

### 3. 算法优化
- **早期过滤**: 在模型推理前过滤无效数据
- **批量tokenization**: 批量处理文本tokenization
- **智能阈值**: 动态调整得分阈值

## 错误处理

### 1. 错误类型
- **验证错误**: 参数格式不正确
- **类型转换错误**: 数据类型不匹配
- **模型错误**: 推理失败或模型不存在
- **网络错误**: ASE服务调用失败

### 2. 错误处理策略
- **渐进式降级**: 部分失败时返回可用结果
- **详细错误信息**: 提供具体的错误原因和建议
- **链路追踪**: 记录错误发生的完整调用链

## 配置管理

### 1. 模型配置
```toml
[[rank_models]]
name = "model_name"
timeout = 5000
batch_size = 32
```

### 2. 关键配置项
- **timeout**: 模型推理超时时间
- **batch_size**: 批处理大小
- **threshold**: 得分阈值
- **topK**: 返回结果数量限制

## 监控和追踪

### 1. 指标监控
- **请求量**: QPS和并发数
- **延迟**: 平均响应时间和P99延迟
- **错误率**: 各类错误的发生率
- **资源使用**: CPU、内存、网络使用情况

### 2. 链路追踪
- **请求链路**: 完整的请求处理链路
- **性能分析**: 各阶段耗时分析
- **错误定位**: 快速定位问题原因

## 部署和运维

### 1. 部署要求
- **Go版本**: 1.23.3+
- **依赖库**: skynet-pandora-go框架
- **外部依赖**: ASE推理服务、Tokenizer服务

### 2. 健康检查
- **服务状态**: 检查服务是否正常运行
- **依赖检查**: 验证外部依赖是否可用
- **性能监控**: 持续监控性能指标

### 3. 扩展性
- **水平扩展**: 支持多实例部署
- **模型热更新**: 支持模型配置动态更新
- **负载均衡**: 支持多种负载均衡策略

## 开发和测试

### 1. 开发环境搭建
```bash
# 构建服务
go build -tags=rank -o rank_server .

# 运行服务
./rank_server server -c ./config/rank
```

### 2. 测试策略
- **单元测试**: 核心功能单元测试
- **集成测试**: 端到端集成测试
- **性能测试**: 压力测试和性能基准测试
- **错误测试**: 异常情况处理测试

### 3. 代码质量
- **代码审查**: 严格的代码审查流程
- **静态分析**: 使用静态分析工具
- **测试覆盖率**: 保持高测试覆盖率

## 总结

Rank Service作为lynxiao-ai-search的核心组件，承担着搜索结果精排的重要职责。通过合理的架构设计、性能优化和错误处理，确保了服务的高可用性和高性能。持续的监控和优化使得服务能够满足不断增长的业务需求。