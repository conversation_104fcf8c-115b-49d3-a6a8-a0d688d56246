package service

import (
	"fmt"
	"strings"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/service/intervene"
	proto_qubase "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/qubase"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type QueryCorrectService struct {
	Ctx  *pandora_context.PandoraContext
	Req  *pandora_proto.PandoraRequestMessage[proto_qubase.ProtoCorrectRequestPayload]
	Resp *pandora_proto.PandoraResponseMessage[proto_qubase.ProtoCorrectResponsePayload]
}

func (s *QueryCorrectService) checkRequest() *errtypes.SelfError {
	if len(s.Req.Header.TraceId) == 0 {
		return selferrors.CommonError_MissingField.Detaild(fmt.Sprintf("%s: traceId is empty", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	}
	if len(s.Req.Payload.Texts) == 0 {
		return selferrors.CommonError_InvalidInput.Detaild(fmt.Sprintf("%s: empty input texts", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	}
	for _, text := range s.Req.Payload.Texts {
		if len(text) == 0 {
			return selferrors.CommonError_InvalidInput.Detaild(fmt.Sprintf("%s: empty input texts", goboot.BootConf().DefaultInstance().Conf.ServiceName))
		}
	}
	return nil
}

func (s *QueryCorrectService) correct(query string, code string) (string, error) {
	if !intervene.QubaseIntervene.CorrectEnabledMap[code] {
		return query, fmt.Errorf("correct not enabled: %s", code)
	}
	correctMap, ok := intervene.QubaseIntervene.CorrectMap[code]
	if !ok {
		return query, fmt.Errorf("correct map not found: %s", code)
	}

	// 1. 提前校验并获取替换词表
	replacements, exists := intervene.QubaseIntervene.CorrectListMap[code]
	if !exists || len(*replacements) == 0 {
		return query, fmt.Errorf("correct list not found")
	}

	// 2. 使用最长匹配算法找到所有需要替换的词
	matches := s.findLongestMatchesForService(query, *replacements)
	if len(matches) == 0 {
		return query, nil
	}

	// 3. 按照匹配到的词进行替换
	currentQuery := query
	for _, match := range matches {
		if replacement, ok := correctMap.Get(match.word); ok {
			currentQuery = strings.ReplaceAll(currentQuery, match.word, replacement)
		}
	}
	return currentQuery, nil
}

// matchInfoService 存储匹配信息（为了避免与 correct.go 中的结构冲突）
type matchInfoService struct {
	word  string
	start int
	end   int
}

// findLongestMatchesForService 实现最长匹配算法，处理重叠嵌套词
func (s *QueryCorrectService) findLongestMatchesForService(query string, candidates []string) []matchInfoService {
	var allMatches []matchInfoService

	// 1. 找到所有可能的匹配
	for _, word := range candidates {
		start := 0
		for {
			idx := strings.Index(query[start:], word)
			if idx == -1 {
				break
			}
			actualStart := start + idx
			allMatches = append(allMatches, matchInfoService{
				word:  word,
				start: actualStart,
				end:   actualStart + len(word),
			})
			start = actualStart + 1 // 继续查找重叠的匹配
		}
	}

	// 2. 如果没有匹配，直接返回
	if len(allMatches) == 0 {
		return nil
	}

	// 3. 按起始位置排序，如果起始位置相同则按长度降序排序（优先选择更长的词）
	for i := 0; i < len(allMatches)-1; i++ {
		for j := i + 1; j < len(allMatches); j++ {
			if allMatches[i].start > allMatches[j].start ||
				(allMatches[i].start == allMatches[j].start && len(allMatches[i].word) < len(allMatches[j].word)) {
				allMatches[i], allMatches[j] = allMatches[j], allMatches[i]
			}
		}
	}

	// 4. 贪心算法选择不重叠的最长匹配
	var result []matchInfoService
	lastEnd := -1

	for _, match := range allMatches {
		if match.start >= lastEnd {
			result = append(result, match)
			lastEnd = match.end
		}
	}

	return result
}

func (s *QueryCorrectService) parse() {
	span := s.Ctx.RootSpan().AddSpan("Query纠错")
	defer func() {
		span.Finish()
		span.TraceInfo("requestInfo", s.Req)
		span.TraceInfo("responseInfo", s.Resp)
	}()
	// query纠错
	codes := s.Req.Payload.CorrectStrategy
	results := s.Req.Payload.Texts
	err := s.checkRequest()
	if err != nil {
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		s.Resp.Payload = proto_qubase.ProtoCorrectResponsePayload{
			Results: []string{},
		}
		return
	}
	for i, query := range s.Req.Payload.Texts {
		if len(results[i]) == 0 {
			results[i] = query
		}
		for _, code := range codes {
			q, err := s.correct(query, code)
			if err != nil {
				continue
			}
			results[i] = q

		}
	}
	s.Resp.Payload = proto_qubase.ProtoCorrectResponsePayload{
		Results: results,
	}

}

func (s *QueryCorrectService) HttpHandler() {
	span := s.Ctx.RootSpan().AddSpan(s.Req.Header.AppId)
	defer span.Finish()
	s.parse()
}
