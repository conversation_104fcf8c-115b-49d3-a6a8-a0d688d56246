package service

import (
	"fmt"
	"strings"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/service/intervene"
	proto_qubase "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/qubase"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type QueryCorrectService struct {
	Ctx  *pandora_context.PandoraContext
	Req  *pandora_proto.PandoraRequestMessage[proto_qubase.ProtoCorrectRequestPayload]
	Resp *pandora_proto.PandoraResponseMessage[proto_qubase.ProtoCorrectResponsePayload]
}

func (s *QueryCorrectService) checkRequest() *errtypes.SelfError {
	if len(s.Req.Header.TraceId) == 0 {
		return selferrors.CommonError_MissingField.Detaild(fmt.Sprintf("%s: traceId is empty", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	}
	if len(s.Req.Payload.Texts) == 0 {
		return selferrors.CommonError_InvalidInput.Detaild(fmt.Sprintf("%s: empty input texts", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	}
	for _, text := range s.Req.Payload.Texts {
		if len(text) == 0 {
			return selferrors.CommonError_InvalidInput.Detaild(fmt.Sprintf("%s: empty input texts", goboot.BootConf().DefaultInstance().Conf.ServiceName))
		}
	}
	return nil
}

func (s *QueryCorrectService) correct(query string, code string) (string, error) {
	if !intervene.QubaseIntervene.CorrectEnabledMap[code] {
		return query, fmt.Errorf("correct not enabled: %s", code)
	}
	correctMap, ok := intervene.QubaseIntervene.CorrectMap[code]
	if !ok {
		return query, fmt.Errorf("correct map not found: %s", code)
	}

	// 1. 提前校验并获取替换词表
	replacements, exists := intervene.QubaseIntervene.CorrectListMap[code]
	if !exists || len(*replacements) == 0 {
		return query, fmt.Errorf("correct list not found")
	}

	// 3. 单次遍历完成所有替换
	for _, target := range *replacements {
		if replacement, ok := correctMap.Get(target); ok && strings.Contains(query, target) {
			query = strings.ReplaceAll(query, target, replacement)
		}
	}
	return query, nil
}

func (s *QueryCorrectService) parse() {
	span := s.Ctx.RootSpan().AddSpan("Query纠错")
	defer func() {
		span.Finish()
		span.TraceInfo("requestInfo", s.Req)
		span.TraceInfo("responseInfo", s.Resp)
	}()
	// query纠错
	codes := s.Req.Payload.CorrectStrategy
	results := s.Req.Payload.Texts
	err := s.checkRequest()
	if err != nil {
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		s.Resp.Payload = proto_qubase.ProtoCorrectResponsePayload{
			Results: []string{},
		}
		return
	}
	for i, query := range s.Req.Payload.Texts {
		if len(results[i]) == 0 {
			results[i] = query
		}
		for _, code := range codes {
			q, err := s.correct(query, code)
			if err != nil {
				continue
			}
			results[i] = q

		}
	}
	s.Resp.Payload = proto_qubase.ProtoCorrectResponsePayload{
		Results: results,
	}

}

func (s *QueryCorrectService) HttpHandler() {
	span := s.Ctx.RootSpan().AddSpan(s.Req.Header.AppId)
	defer span.Finish()
	s.parse()
}
