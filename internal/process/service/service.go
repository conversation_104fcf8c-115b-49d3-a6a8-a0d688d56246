package service

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter/distinct"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter/limit"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter/sift"
	sortTools "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/sort"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/util"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"github.com/samber/lo"
)

// 对象池优化：复用Processor实例
var processorPool = sync.Pool{
	New: func() any {
		return &filter.Processor{
			Filters: make(map[string]filter.Filter),
		}
	},
}

// FilterService 过滤服务
type FilterService struct {
}

// 优化：统一类型转换，减少重复代码
func convertToFloat64(value interface{}) float64 {
	switch v := value.(type) {
	case int:
		return float64(v)
	case int32:
		return float64(v)
	case int64:
		return float64(v)
	case float32:
		return float64(v)
	case float64:
		return v
	default:
		return 0
	}
}

func (dp *FilterService) setDistinct(processor *filter.Processor, dis []*proto_process.Distinct) []string {
	names := make([]string, len(dis))
	for i, d := range dis {
		name := "distinct_" + strconv.Itoa(i) // 优化：避免fmt.Sprintf
		processor.SetFilter(name, &distinct.Distinct{
			DistinctFields: d.Cond,
			ReserveFields:  d.Fields,
			KeySelector:    selector,
			ChooseItem:     chooseItem,
		})
		names[i] = name
	}
	return names
}

func (dp *FilterService) setSift(processor *filter.Processor, condition *proto_process.SiftCondition) string {
	siftFilter := &sift.Sift{Condition: condition}

	name := "sift"
	processor.SetFilter(name, siftFilter)
	return name
}

func (dp *FilterService) setLimit(processor *filter.Processor, cond []*proto_process.LimitField) []string {
	names := make([]string, len(cond))
	for i, c := range cond {
		name := "limit_" + strconv.Itoa(i) // 优化：避免fmt.Sprintf
		processor.SetFilter(name, &limit.Limit{LimitField: c})
		names[i] = name
	}
	return names
}

// Process 数据处理流程
func (dp *FilterService) Process(span *pandora_span.Span, payload *proto_process.Payload) error {
	// 优化：预估filterNames容量，避免切片扩容
	capacity := 0
	if payload.Sift != nil {
		capacity++
	}
	if payload.Distinct != nil {
		capacity += len(payload.Distinct)
	}
	if payload.Limit != nil {
		capacity += len(payload.Limit)
	}
	filterNames := make([]string, 0, capacity)

	// 优化：使用对象池获取processor实例
	processor := processorPool.Get().(*filter.Processor)
	defer func() {
		// 清理后归还到对象池
		for k := range processor.Filters {
			delete(processor.Filters, k)
		}
		processorPool.Put(processor)
	}()

	// 筛选
	if payload.Sift != nil {
		name := dp.setSift(processor, payload.Sift)
		filterNames = append(filterNames, name)
	}
	// 去重
	if payload.Distinct != nil {
		payload.Distinct = lo.Filter(payload.Distinct, func(item *proto_process.Distinct, _ int) bool { return item != nil })
		filterNames = append(filterNames, dp.setDistinct(processor, payload.Distinct)...)
	}
	// 限制
	if payload.Limit != nil {
		payload.Limit = lo.Filter(payload.Limit, func(item *proto_process.LimitField, _ int) bool { return item != nil })
		filterNames = append(filterNames, dp.setLimit(processor, payload.Limit)...)
	}

	// 并行处理docs：当数据块数量大于阈值时启用并行处理
	if len(payload.Data) > 1 {
		err := dp.processDataParallel(span, processor, filterNames, payload.Data)
		if err != nil {
			return err
		}
	} else {
		// 单个数据块直接处理
		for i := range payload.Data {
			docs, err := processor.Apply(span, filterNames, payload.Data[i].Docs)
			if err != nil {
				return err
			}
			payload.Data[i].Docs = docs
		}
	}

	// 处理完成之后再进行排序
	if payload.SortList != nil {
		err := sortTools.Sort(span, payload.SortList, payload)
		if err != nil {
			return err
		}
	}

	return nil
}

// processDataParallel 并行处理多个数据块
func (dp *FilterService) processDataParallel(span *pandora_span.Span, templateProcessor *filter.Processor, filterNames []string, dataBlocks []*proto_process.ReqData) error {
	var wg sync.WaitGroup
	errChan := make(chan error, len(dataBlocks))

	// 并行处理每个数据块
	for i := range dataBlocks {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// 为每个goroutine创建独立的processor实例
			processor := processorPool.Get().(*filter.Processor)
			defer func() {
				// 清理后归还到对象池
				for k := range processor.Filters {
					delete(processor.Filters, k)
				}
				processorPool.Put(processor)
			}()

			// 复制过滤器配置到新的processor
			for name, filter := range templateProcessor.Filters {
				processor.Filters[name] = filter
			}

			// 处理数据
			docs, err := processor.Apply(span, filterNames, dataBlocks[index].Docs)
			if err != nil {
				errChan <- fmt.Errorf("processing data block %d failed: %w", index, err)
				return
			}
			dataBlocks[index].Docs = docs
		}(i)
	}

	wg.Wait()
	close(errChan)

	// 检查是否有错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}

func selector(item map[string]any, fields []string) (string, error) {
	values := make([]any, len(fields))
	for i, field := range fields {
		// 字段不存在，返回随机字符串,表示不去重
		if value, err := util.GetField(field, item); err != nil {
			values[i], _ = util.RandomString(16)
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("distinct selector fails to get %s, err: %v", field, err))
		} else {
			// 进行特殊字符处理
			switch value := value.(type) {
			case string:
				value = distinct.Clean(value)
			default:
				fmt.Printf("the type is %T\n", value)
			}

			values[i] = value
		}
	}
	res := util.Concat(values)
	return res, nil
}

func chooseItem(item1, item2 map[string]any, params []*proto_process.DistinctField) (map[string]any, error) {
	compare := func(val1, val2 float64, operator string) int {
		switch operator {
		case "max":
			if val1 > val2 {
				return 1
			} else if val1 < val2 {
				return -1
			}
			// in操作,优先val小的
		case "min", "in":
			if val1 > val2 {
				return -1
			} else if val1 < val2 {
				return 1
			}
		}
		return 0
	}

	for _, param := range params {
		var val1, val2 float64
		field1, err := util.GetField(param.Field, item1)
		if err != nil {
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("distinct chooseItem fails to get %s, err: %v", param.Field, err))
			return item2, nil
		}
		field2, err := util.GetField(param.Field, item2)
		if err != nil {
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("distinct chooseItem fails to get %s, err: %v", param.Field, err))
			return item1, nil
		}

		switch param.Type {
		case "number":
			// 优化：使用统一的类型转换函数
			val1 = convertToFloat64(field1)
			val2 = convertToFloat64(field2)
		case "string":
			val1 = float64(util.Contains2(strings.Split(param.Value, ","), fmt.Sprintf("%v", field1)))
			val2 = float64(util.Contains2(strings.Split(param.Value, ","), fmt.Sprintf("%v", field2)))
		default:
			continue
		}

		if result := compare(val1, val2, param.Operator); result != 0 {
			if result == 1 {
				return item1, nil
			}
			return item2, nil
		}
	}

	return item1, nil
}
