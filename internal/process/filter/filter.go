package filter

import (
	"fmt"

	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
)

// Filter 接口定义
type Filter interface {
	Apply(input []map[string]any) ([]map[string]any, error)
}

// Processor 处理器,可配置多个过滤组件
type Processor struct {
	Filters      map[string]Filter                // 过滤组件
	OutputFormat func(item map[string]any) string // 可配置输出格式
}

// SetFilter 设置过滤器
func (p *Processor) SetFilter(name string, filter Filter) {
	p.Filters[name] = filter
}

// Apply 应用过滤器
func (p *Processor) Apply(span *pandora_span.Span, filterNames []string, input []map[string]any) ([]map[string]any, error) {
	var err error
	for _, name := range filterNames {
		childSpan := span.AddSpan(name)
		input, err = p.Filters[name].Apply(input)

		if err != nil {
			childSpan.TraceInfo("error", err.Error())
			childSpan.Finish()
			return nil, fmt.Errorf("%s failed, error: %v", name, err)
		}
		childSpan.Finish()
	}
	return input, nil
}
