package sift

import (
	"fmt"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/util"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

// Sift 根据 Condition 结构体进行筛选
type Sift struct {
	Condition *proto_process.SiftCondition
}

// 检查是否实现Filter接口
var _ filter.Filter = (*Sift)(nil)

func (s *Sift) Apply(input []map[string]any) ([]map[string]any, error) {
	var result []map[string]any
	for _, item := range input {
		if eval, _ := s.evaluateCondition(item, s.Condition); eval {
			result = append(result, item)
		}
	}
	return result, nil
}

func (s *Sift) evaluateCondition(item map[string]any, condition *proto_process.SiftCondition) (bool, error) {
	if condition == nil || condition.IsEmpty() {
		return true, nil
	}

	if len(condition.SubConditions) > 0 {
		matches := make([]bool, len(condition.SubConditions))
		for i, subCondition := range condition.SubConditions {
			matches[i], _ = s.evaluateCondition(item, subCondition)
		}
		switch condition.Logic {
		case "AND":
			return all(matches), nil
		case "OR":
			return some(matches), nil
		}
	}

	field, err := util.GetField(condition.Field, item)
	if err != nil {
		// 取不到值，默认为false
		goboot.Logger().DefaultInstance().Error(fmt.Sprintf("sift evaluate get field %s error: %v", condition.Field, err))
		return false, err
	}
	return util.EvaluateOperator(field, condition.FieldType, condition.Operator, condition.Origin), nil
}

func all(matches []bool) bool {
	for _, match := range matches {
		if !match {
			return false
		}
	}
	return true
}

func some(matches []bool) bool {
	for _, match := range matches {
		if match {
			return true
		}
	}
	return false
}
