package limit

import (
	"fmt"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/util"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
)

// Limit 根据特定条件取Top K
type Limit struct {
	*proto_process.LimitField
}

// 检查是否实现Filter接口
var _ filter.Filter = (*Limit)(nil)

// Apply 过滤数据
func (l *Limit) Apply(input []map[string]any) ([]map[string]any, error) {
	if len(input) <= l.Num {
		return input, nil
	}

	res := make([]map[string]any, 0, len(input))
	count := 0

	for _, v := range input {
		value, err := util.GetField(l.Key, v)
		if err != nil {
			// 数据取不到，不在限制条件，添加
			// log.GetLogger().ErrorF("limit get field %s error: %v", l.Key, err)
			res = append(res, v)
			continue
		}

		if util.Contains(strings.Split(l.Cond, ","), fmt.Sprintf("%v", value)) > -1 {
			count++
			if count <= l.Num {
				res = append(res, v)
			}
		} else {
			res = append(res, v)
		}
	}

	return res, nil
}
