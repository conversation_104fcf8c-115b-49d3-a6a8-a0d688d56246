package sort

import (
	"fmt"
	"sort"
	"sync"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/util"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
)

func Sort(span *pandora_span.Span, sortList []*proto_process.SortField, payload *proto_process.Payload) error {
	if len(sortList) == 0 || len(payload.Data) == 0 {
		return nil
	}

	// 并行处理每个数据块的排序
	if len(payload.Data) > 1 {
		return sortDataParallel(sortList, payload.Data)
	}
	// 单个数据块使用优化后的排序
	return sortDataOptimized(sortList, payload.Data[0])
}

// sortDataParallel 并行处理多个数据块的排序
func sortDataParallel(sortList []*proto_process.SortField, dataBlocks []*proto_process.ReqData) error {
	var wg sync.WaitGroup
	errChan := make(chan error, len(dataBlocks))

	// 并行处理每个数据块的排序
	for i := range dataBlocks {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			if err := sortDataOptimized(sortList, dataBlocks[index]); err != nil {
				errChan <- fmt.Errorf("sorting data block %d failed: %w", index, err)
			}
		}(i)
	}

	wg.Wait()
	close(errChan)

	// 检查是否有错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}

// sortDataOptimized 使用预计算排序键的优化排序
func sortDataOptimized(sortList []*proto_process.SortField, reqData *proto_process.ReqData) error {
	if len(reqData.Docs) <= 1 {
		return nil
	}

	docs := reqData.Docs

	// 预计算所有文档的排序键
	sortKeys := make([][]interface{}, len(docs))

	// 当文档数量较少时使用串行计算，避免goroutine开销
	if len(docs) < 100 {
		// 串行计算排序键
		for i := range docs {
			sortKeys[i] = make([]interface{}, len(sortList))
			for j, sortField := range sortList {
				value, err := util.GetField(sortField.Field, docs[i])
				if err != nil {
					sortKeys[i][j] = nil // 字段不存在使用nil
				} else {
					sortKeys[i][j] = value
				}
			}
		}
	} else {
		// 并行计算每个文档的排序键
		var keyWg sync.WaitGroup
		for i := range docs {
			keyWg.Add(1)
			go func(docIndex int) {
				defer keyWg.Done()

				sortKeys[docIndex] = make([]interface{}, len(sortList))
				for j, sortField := range sortList {
					value, err := util.GetField(sortField.Field, docs[docIndex])
					if err != nil {
						sortKeys[docIndex][j] = nil // 字段不存在使用nil
					} else {
						sortKeys[docIndex][j] = value
					}
				}
			}(i)
		}
		keyWg.Wait()
	}

	// 使用预计算的键进行排序
	sort.Slice(docs, func(i, j int) bool {
		return compareWithPrecomputedKeys(sortKeys[i], sortKeys[j], sortList)
	})

	return nil
}

// compareWithPrecomputedKeys 使用预计算的键进行比较
func compareWithPrecomputedKeys(keysI, keysJ []any, sortList []*proto_process.SortField) bool {
	for idx, sortField := range sortList {
		valueI := keysI[idx]
		valueJ := keysJ[idx]

		// 如果任一值为nil，跳过这个字段
		if valueI == nil || valueJ == nil {
			continue
		}

		// 如果两个值相等，继续比较下一个排序字段
		if compareValue(valueI, "==", valueJ) {
			continue
		}

		// 根据排序方向返回比较结果
		switch sortField.Sort {
		case model.SORT_TYPE_DESC:
			return compareValue(valueI, ">", valueJ)
		case model.SORT_TYPE_ASC:
			return compareValue(valueI, "<", valueJ)
		}
	}
	return false // 所有字段都相等时保持原有顺序
}

func compareValue(value1 any, operator string, value2 any) bool {

	var fieldType string
	// v := reflect.TypeOf(value1)
	switch v := value1.(type) {
	case int:
		fieldType = model.TYPE_INT
	case int32:
		fieldType = model.TYPE_LONG
	case int64:
		fieldType = model.TYPE_LONG
	case float64:
		fieldType = model.TYPE_DOUBLE
	case float32:
		fieldType = model.TYPE_FLOAT32
	case string:
		fieldType = model.TYPE_STRING
	default:
		fmt.Printf("Unsupported field type: %T\n", v)

	}

	return util.EvaluateOperator(value1, fieldType, operator, value2)
}
