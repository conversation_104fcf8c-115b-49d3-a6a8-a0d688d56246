# Process 模块架构说明

## 模块概述

`internal/process` 模块是搜索结果后处理的核心组件，负责对搜索结果进行筛选(Sift)、去重(Distinct)、限制(Limit)和排序(Sort)等操作。

## 架构设计

### 目录结构

```
internal/process/
├── api.go                    # HTTP API 接口层，处理请求入口
├── model/
│   └── const.go             # 数据类型常量定义
├── service/
│   └── service.go           # 业务逻辑服务层，协调各个过滤器
├── filter/                  # 过滤器组件
│   ├── filter.go            # 过滤器接口定义
│   ├── distinct/            # 去重过滤器
│   │   ├── distinct.go      # 去重逻辑实现
│   │   └── regexp_character.go # 正则字符处理工具
│   ├── limit/
│   │   └── limit.go         # 限制过滤器，按条件限制数量
│   └── sift/
│       └── sift.go          # 筛选过滤器，按条件筛选数据
├── sort/
│   └── sort.go              # 排序处理器
└── util/                    # 工具函数
    ├── concat.go            # 字符串拼接工具
    ├── contain.go           # 包含判断工具
    ├── evaluate.go          # 条件表达式求值
    ├── field.go             # 字段值获取工具
    └── random.go            # 随机字符串生成
```

### 核心组件关系

```mermaid
graph TB
    A[HTTP Request] --> B[api.go]
    B --> C[service.go]
    C --> D[Processor]
    D --> E[Sift Filter]
    D --> F[Distinct Filter]
    D --> G[Limit Filter]
    E --> H[Sort]
    F --> H
    G --> H
    H --> I[HTTP Response]
```

## 核心组件详解

### 1. API 层 (api.go)

**作用**: HTTP 接口入口，负责请求解析和响应处理

**关键函数**:
- `Process()`: 主处理函数，接收 Pandora 请求并返回响应
- `Init()`: 服务初始化，注册 HTTP 路由

**处理流程**:
1. 接收 HTTP 请求
2. 参数验证
3. 调用 Service 层处理
4. 返回处理结果

### 2. Service 层 (service.go)

**作用**: 业务逻辑协调，管理过滤器链的执行

**关键结构**:
```go
type FilterService struct {
    Processor *filter.Processor
}
```

**核心方法**:
- `Process()`: 协调整个处理流程
- `setSift()`: 设置筛选过滤器
- `setDistinct()`: 设置去重过滤器
- `setLimit()`: 设置限制过滤器

**执行顺序**:
1. Sift (筛选)
2. Distinct (去重)
3. Limit (限制)
4. Sort (排序)

### 3. Filter 框架 (filter/filter.go)

**核心接口**:
```go
type Filter interface {
    Apply(input []map[string]any) ([]map[string]any, error)
}
```

**处理器结构**:
```go
type Processor struct {
    Filters      map[string]Filter
    OutputFormat func(item map[string]any) string
    sync.Mutex
}
```

**工作原理**:
- 所有过滤器实现统一的 `Filter` 接口
- `Processor` 管理过滤器链的执行
- 支持动态添加和配置过滤器

### 4. 筛选过滤器 (sift/sift.go)

**功能**: 根据条件表达式筛选数据

**核心结构**:
```go
type Sift struct {
    Condition *proto_process.SiftCondition
}
```

**支持的条件**:
- 字段比较 (==, !=, >, <, >=, <=)
- 包含判断 (in)
- 时间范围 (last_n_hours, last_n_days, last_n_months)
- 逻辑组合 (AND, OR)

**处理逻辑**:
1. 解析条件表达式
2. 递归处理嵌套条件
3. 对每个数据项进行条件求值
4. 返回满足条件的数据项

### 5. 去重过滤器 (distinct/distinct.go)

**功能**: 根据指定字段去重，支持保留策略

**核心结构**:
```go
type Distinct struct {
    KeySelector    func(item map[string]any, fields []string) (string, error)
    ChooseItem     func(item1, item2 map[string]any, params []*proto_process.DistinctField) (map[string]any, error)
    ReserveFields  []*proto_process.DistinctField
    DistinctFields []string
}
```

**去重策略**:
- 根据指定字段组合生成唯一键
- 支持数值字段的 max/min 选择
- 支持字符串字段的优先级选择
- 保持原始顺序

**字符清理** (regexp_character.go):
- 移除标点符号和特殊字符
- 统一空白字符处理
- 支持中文标点符号处理

### 6. 限制过滤器 (limit/limit.go)

**功能**: 按条件限制特定类型数据的数量

**核心结构**:
```go
type Limit struct {
    *proto_process.LimitField
    conditionSet map[string]bool  // 条件缓存
    once         sync.Once        // 确保初始化一次
}
```

**工作原理**:
1. 解析限制条件 (逗号分隔的值列表)
2. 遍历数据，统计匹配条件的数量
3. 超过限制数量的数据被过滤掉
4. 不匹配条件的数据保留

### 7. 排序处理器 (sort/sort.go)

**功能**: 多字段排序，支持并行处理

**核心结构**:
```go
type sortCache struct {
    values [][]any
    valid  [][]bool
}
```

**排序策略**:
- 支持多字段排序
- 预计算排序键值，减少重复计算
- 支持升序 (asc) 和降序 (desc)
- 并行处理多个数据集

**性能优化**:
- 使用对象池缓存排序数据
- 预计算所有排序字段的值
- 并发处理不同的数据集

### 8. 工具函数 (util/)

**field.go**: 字段值获取
```go
func GetField(field string, item bson.M) (any, error)
```
- 使用 expr 表达式引擎
- 支持嵌套字段访问
- 统一错误处理

**evaluate.go**: 条件求值
```go
func EvaluateOperator(field any, fieldType string, operator string, value any) bool
```
- 支持多种数据类型比较
- 时间条件处理
- 字符串长度和包含判断

**contain.go**: 包含判断
```go
func Contains[T constraints.Ordered](s []T, e T) int
```
- 泛型实现，支持多种类型
- 返回索引位置，不存在返回 -1

## 数据流处理

### 请求数据结构

```go
type Payload struct {
    Sift      *SiftCondition     // 筛选条件
    Distinct  []*Distinct        // 去重配置
    Limit     []*LimitField      // 限制配置
    SortList  []*SortField       // 排序配置
    Data      []*ReqData         // 待处理数据
}
```

### 处理流程

1. **请求解析**: API 层解析 HTTP 请求
2. **参数验证**: 验证请求参数格式
3. **过滤器配置**: Service 层根据请求配置过滤器
4. **数据处理**: 按顺序应用过滤器链
5. **排序处理**: 对处理结果进行排序
6. **响应返回**: 返回处理后的数据

### 错误处理

- 字段获取失败：记录错误日志，继续处理
- 过滤器执行失败：中断处理，返回错误
- 上下文取消：支持超时和取消操作

## 使用示例

### 基本使用

```go
// 初始化服务
err := process.Init()
if err != nil {
    log.Fatal(err)
}

// 请求处理由框架自动调用
// POST /process/api/v2
```

### 请求示例

```json
{
  "sift": {
    "field": "category",
    "operator": "in",
    "value": "tech,news"
  },
  "distinct": [{
    "cond": ["title"],
    "fields": [{
      "field": "score",
      "operator": "max",
      "type": "number"
    }]
  }],
  "limit": [{
    "key": "source",
    "cond": "news,blog",
    "num": 10
  }],
  "sortList": [{
    "field": "score",
    "sort": "desc"
  }],
  "data": [{
    "docs": [
      {"title": "文章1", "score": 0.9, "category": "tech"},
      {"title": "文章2", "score": 0.8, "category": "news"}
    ]
  }]
}
```

## 扩展性设计

### 添加新过滤器

1. 实现 `Filter` 接口
2. 在 Service 层添加配置方法
3. 在处理流程中集成

### 自定义条件求值

1. 扩展 `EvaluateOperator` 函数
2. 添加新的操作符支持
3. 更新相关测试用例

### 性能监控

- 使用 span 跟踪处理时间
- 记录关键性能指标
- 支持分布式链路追踪

## 最佳实践

1. **内存管理**: 使用对象池减少内存分配
2. **并发控制**: 使用 errgroup 管理并发任务
3. **错误处理**: 包装错误信息，提供上下文
4. **性能优化**: 缓存计算结果，减少重复操作
5. **日志记录**: 记录关键操作和错误信息