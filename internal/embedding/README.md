# Embedding Service 文档

## 概述

Embedding Service 是一个高性能的向量化服务，负责将文本转换为向量表示，支持多种模型和批处理功能。该服务采用微服务架构，具有良好的扩展性和并发处理能力。

## 目录结构

```
internal/embedding/
├── api.go                    # HTTP API处理层
├── entity/                   # 数据结构定义
│   ├── const.go             # 常量定义
│   └── custom.go            # 自定义配置结构
├── service/                 # 业务逻辑层
│   └── embedding_service.go # 核心服务实现
└── README.md               # 本文档
```

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "客户端"
        C[HTTP客户端]
    end
    
    subgraph "API层"
        A[api.go]
        A --> V[请求验证]
        A --> H[响应处理]
    end
    
    subgraph "业务逻辑层"
        S[embedding_service.go]
        S --> M[模型管理]
        S --> B[批处理]
        S --> P[并发处理]
    end
    
    subgraph "配置管理"
        E[entity/custom.go]
        E --> MC[模型配置]
    end
    
    subgraph "外部依赖"
        ASE[ASE SDK V2]
        GOB[GoBoot框架]
    end
    
    C --> A
    A --> S
    S --> E
    S --> ASE
    A --> GOB
    
    style A fill:#e1f5fe
    style S fill:#f3e5f5
    style E fill:#e8f5e8
    style ASE fill:#fff3e0
```

### 组件架构图

```mermaid
graph LR
    subgraph "Embedding Service"
        API[API Handler]
        SVC[Service Layer]
        CFG[Config Manager]
        
        API --> SVC
        SVC --> CFG
    end
    
    subgraph "核心功能"
        VAL[请求验证]
        BATCH[批处理]
        INFER[模型推理]
        MERGE[结果合并]
        
        SVC --> VAL
        SVC --> BATCH
        SVC --> INFER
        SVC --> MERGE
    end
    
    subgraph "外部服务"
        ASE_SDK[ASE SDK V2]
        PANDORA[Pandora Context]
        
        INFER --> ASE_SDK
        API --> PANDORA
    end
    
    style API fill:#bbdefb
    style SVC fill:#c8e6c9
    style CFG fill:#ffcdd2
```

## 核心功能

### 1. 请求处理流程

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Service
    participant ASE
    
    Client->>API: POST /embedding/api/v2
    API->>API: 参数验证
    API->>Service: Process(ctx, span, req)
    Service->>Service: 获取模型配置
    Service->>Service: 文本批次划分
    
    par 并发处理
        Service->>ASE: 推理请求1
        Service->>ASE: 推理请求2
        Service->>ASE: 推理请求N
    end
    
    ASE-->>Service: 返回向量结果
    Service->>Service: 合并批次结果
    Service-->>API: 返回最终结果
    API-->>Client: HTTP响应
```

### 2. 批处理机制

服务支持智能批处理，可以根据配置的`batch_size`自动将大量文本分批处理，提高处理效率：

```go
// 批处理示例
textsBatch := lo.Chunk(req.Texts, aseConf.BatchSize)
resBatch := make([][][]float32, len(textsBatch))
```

### 3. 并发处理

使用`errgroup`实现并发处理多个批次，显著提升性能：

```go
var g errgroup.Group
for batchID, texts := range textsBatch {
    g.Go(func() error {
        // 并发处理每个批次
        return processBatch(texts, batchID)
    })
}
```

## 配置说明

### 模型配置 (ModelConfig)

```go
type ModelConfig struct {
    base_model.BaseModel
    Timeout   int  `toml:"timeout"`    // 超时时间(ms)
    ResDim    int  `toml:"res_dim"`    // 向量维度
    Default   bool `toml:"default"`    // 是否为默认模型
    BatchSize int  `toml:"batch_size"` // 批处理大小
}
```

### 配置示例

```toml
[embedding_models.model1]
timeout = 5000
res_dim = 768
default = true
batch_size = 32
```

## API接口

### 请求格式

```
POST /embedding/api/v2
Content-Type: application/json

{
    "header": {
        "appId": "your-app-id"
    },
    "payload": {
        "model": "model_name",  // 可选，不传使用默认模型
        "texts": ["文本1", "文本2", "文本3"]
    }
}
```

### 响应格式

```json
{
    "header": {
        "code": 0,
        "success": "success, cost: 123ms"
    },
    "payload": {
        "model": "model_name",
        "embedding": [
            [0.1, 0.2, 0.3, ...],  // 文本1的向量
            [0.4, 0.5, 0.6, ...],  // 文本2的向量
            [0.7, 0.8, 0.9, ...]   // 文本3的向量
        ]
    }
}
```

## 性能优化

### 已实现的优化

1. **并发处理**: 使用errgroup实现批次并发处理
2. **内存预分配**: 精确预分配切片容量，减少内存分配
3. **批处理**: 智能批次划分，平衡并发度和资源使用
4. **连接池**: 复用ASE SDK连接，减少连接开销
5. **线程安全**: 使用读写锁保护共享资源

### 性能指标

- **并发度**: 支持多批次并发处理
- **内存使用**: 精确预分配，避免频繁扩容
- **响应时间**: 毫秒级响应，带有详细耗时统计
- **吞吐量**: 支持大批量文本处理

## 错误处理

### 错误类型

```go
// 模型不支持
selferrors.EmbeddingError_ModelNotSupport

// 推理失败
selferrors.EmbeddingError_InferFailed

// ASE请求失败
selferrors.EmbeddingError_AseRequestFailed

// 输入参数无效
selferrors.CommonError_InvalidInput
```

### 错误处理流程

```mermaid
graph TD
    A[请求到达] --> B{参数验证}
    B -->|失败| C[返回InvalidInput错误]
    B -->|成功| D{模型检查}
    D -->|模型不存在| E[返回ModelNotSupport错误]
    D -->|模型存在| F[执行推理]
    F -->|ASE调用失败| G[返回AseRequestFailed错误]
    F -->|其他错误| H[返回InferFailed错误]
    F -->|成功| I[返回结果]
```

## 监控与链路追踪

### Span结构

```
embedding handler
├── appId span
└── embedding
    ├── embedding-infer
    │   ├── batch-1
    │   ├── batch-2
    │   └── batch-n
    └── batch-merge
```

### 关键指标

- **处理时间**: 每个环节的耗时统计
- **批次信息**: 批次数量和大小
- **并发度**: 同时处理的批次数
- **错误率**: 各类错误的发生频率

## 部署与运维

### 初始化流程

1. 注册自定义模型配置
2. 检查ASE SDK依赖
3. 初始化服务实例
4. 注册HTTP路由

### 健康检查

- 模型配置检查
- ASE SDK连接状态
- 服务响应时间监控

### 扩展性

- 支持动态添加新模型
- 支持水平扩展多实例
- 支持负载均衡

## 常见问题

### Q: 如何添加新模型？

A: 在配置文件中添加新的模型配置，重启服务即可。

### Q: 如何优化性能？

A: 调整`batch_size`参数，平衡并发度和资源使用。

### Q: 如何处理大量文本？

A: 服务自动进行批处理，支持任意数量的文本输入。

## 更新日志

### v1.0.0

- ✅ 修复并发安全问题
- ✅ 优化内存使用
- ✅ 增强错误处理
- ✅ 完善监控链路
- ✅ 添加性能优化

---

*最后更新: 2024-07-09*